import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  IntegrationSettings,
  ConnectionStatus,
  Activity,
  Alert,
  Achievement,
  ApplicationId,
  WSMessage,
  SyncHistory,
  IntelligenceMetrics,
  PerformanceMetrics
} from '../../types/integration';

interface IntegrationState {
  // Settings
  settings: IntegrationSettings;
  
  // Connection Status
  connections: Record<ApplicationId, ConnectionStatus>;
  
  // Activities & Achievements
  activities: Activity[];
  achievements: Achievement[];
  
  // Alerts
  alerts: Alert[];
  unresolvedAlertCount: number;
  
  // Sync History
  syncHistory: SyncHistory[];
  lastSyncTime: Date | null;
  isSyncing: boolean;
  
  // Metrics
  intelligenceMetrics: IntelligenceMetrics | null;
  performanceMetrics: PerformanceMetrics | null;
  
  // AMNA Specific State
  amnaStatus: 'active' | 'idle' | 'processing' | 'error';
  amnaMetrics: {
    intelligenceLevel: string;
    queriesToday: number;
    accuracy: string;
    responseTime: number;
    contextSources: number;
  } | null;
  amnaInsights: any[];
  amnaRecommendations: any[];
  amnaAutomationRules: any[];
  
  // AMNA Actions
  setAmnaStatus: (status: 'active' | 'idle' | 'processing' | 'error') => void;
  setAmnaMetrics: (metrics: any) => void;
  setAmnaInsights: (insights: any[]) => void;
  setAmnaRecommendations: (recommendations: any[]) => void;
  setAmnaAutomationRules: (rules: any[]) => void;
  
  // WebSocket
  wsConnected: boolean;
  wsReconnectAttempts: number;
  
  // Actions
  updateSettings: (settings: Partial<IntegrationSettings>) => void;
  updateConnectionStatus: (appId: ApplicationId, status: Partial<ConnectionStatus>) => void;
  addActivity: (activity: Activity) => void;
  addAlert: (alert: Alert) => void;
  resolveAlert: (alertId: string) => void;
  addAchievement: (achievement: Achievement) => void;
  startSync: (applications?: ApplicationId[]) => void;
  completeSync: (history: SyncHistory) => void;
  updateMetrics: (type: 'intelligence' | 'performance', metrics: any) => void;
  setWebSocketStatus: (connected: boolean) => void;
  processWebSocketMessage: (message: WSMessage) => void;
  clearActivities: () => void;
  reset: () => void;
}

const defaultSettings: IntegrationSettings = {
  eConnectIntegration: true,
  lighthouseIntegration: true,
  trainingIntegration: true,
  vendorIntegration: true,
  winsTracking: true,
  realTimeSync: true,
  syncFrequency: 'immediate',
  intelligenceLevel: 'standard',
  notificationsEnabled: true,
  privacyMode: 'standard',
  dataRetentionDays: 90,
  allowAnonymousAnalytics: true
};

const defaultConnectionStatus: ConnectionStatus = {
  id: 'eConnect',
  name: '',
  status: 'disconnected',
  latency: 0,
  uptime: 0,
  errorCount: 0,
  dataFlow: {
    incoming: 0,
    outgoing: 0,
    trend: 'stable'
  }
};

export const useIntegrationStore = create<IntegrationState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        settings: defaultSettings,
        connections: {
          eConnect: { ...defaultConnectionStatus, id: 'eConnect', name: 'E-Connect' },
          lighthouse: { ...defaultConnectionStatus, id: 'lighthouse', name: 'Lighthouse' },
          training: { ...defaultConnectionStatus, id: 'training', name: 'Training & Vendors' },
          wins: { ...defaultConnectionStatus, id: 'wins', name: 'Wins of the Week' }
        },
        activities: [],
        achievements: [],
        alerts: [],
        unresolvedAlertCount: 0,
        syncHistory: [],
        lastSyncTime: null,
        isSyncing: false,
        intelligenceMetrics: null,
        performanceMetrics: null,
        amnaStatus: 'idle',
        amnaMetrics: null,
        amnaInsights: [],
        amnaRecommendations: [],
        amnaAutomationRules: [],
        wsConnected: false,
        wsReconnectAttempts: 0,

        // Actions
        updateSettings: (newSettings) => set((state) => ({
          settings: { ...state.settings, ...newSettings }
        })),

        updateConnectionStatus: (appId, status) => set((state) => ({
          connections: {
            ...state.connections,
            [appId]: { ...state.connections[appId], ...status }
          }
        })),

        addActivity: (activity) => set((state) => ({
          activities: [activity, ...state.activities.slice(0, 99)] // Keep last 100
        })),

        addAlert: (alert) => set((state) => ({
          alerts: [alert, ...state.alerts],
          unresolvedAlertCount: state.unresolvedAlertCount + (alert.resolved ? 0 : 1)
        })),

        resolveAlert: (alertId) => set((state) => ({
          alerts: state.alerts.map(alert => 
            alert.id === alertId 
              ? { ...alert, resolved: true, resolvedAt: new Date() }
              : alert
          ),
          unresolvedAlertCount: Math.max(0, state.unresolvedAlertCount - 1)
        })),

        addAchievement: (achievement) => set((state) => ({
          achievements: [...state.achievements, achievement]
        })),

        startSync: (applications) => set((state) => ({
          isSyncing: true,
          lastSyncTime: new Date()
        })),

        completeSync: (history) => set((state) => ({
          isSyncing: false,
          syncHistory: [history, ...state.syncHistory.slice(0, 49)] // Keep last 50
        })),

        updateMetrics: (type, metrics) => set((state) => ({
          ...(type === 'intelligence' 
            ? { intelligenceMetrics: metrics }
            : { performanceMetrics: metrics }
          )
        })),

        setWebSocketStatus: (connected) => set((state) => ({
          wsConnected: connected,
          wsReconnectAttempts: connected ? 0 : state.wsReconnectAttempts + 1
        })),

        processWebSocketMessage: (message) => {
          const state = get();
          
          switch (message.type) {
            case 'connection.status':
              if (message.source) {
                state.updateConnectionStatus(message.source, message.payload);
              }
              break;
              
            case 'activity.new':
              state.addActivity(message.payload);
              break;
              
            case 'alert.new':
              state.addAlert(message.payload);
              break;
              
            case 'achievement.earned':
              state.addAchievement(message.payload);
              break;
              
            case 'metrics.updated':
              if (message.payload.type === 'intelligence') {
                state.updateMetrics('intelligence', message.payload.data);
              } else if (message.payload.type === 'performance') {
                state.updateMetrics('performance', message.payload.data);
              }
              break;
              
            default:
              console.log('Unhandled WebSocket message type:', message.type);
          }
        },

        clearActivities: () => set({ activities: [] }),

        // AMNA Actions
        setAmnaStatus: (status) => set({ amnaStatus: status }),
        
        setAmnaMetrics: (metrics) => set({ amnaMetrics: metrics }),
        
        setAmnaInsights: (insights) => set({ amnaInsights: insights }),
        
        setAmnaRecommendations: (recommendations) => set({ amnaRecommendations: recommendations }),
        
        setAmnaAutomationRules: (rules) => set({ amnaAutomationRules: rules }),

        reset: () => set({
          settings: defaultSettings,
          activities: [],
          alerts: [],
          achievements: [],
          unresolvedAlertCount: 0,
          syncHistory: [],
          lastSyncTime: null,
          isSyncing: false
        })
      }),
      {
        name: 'integration-store',
        partialize: (state) => ({
          settings: state.settings,
          achievements: state.achievements
        })
      }
    )
  )
);

// Selectors
export const selectActiveIntegrations = (state: IntegrationState) => 
  Object.values(state.connections).filter(conn => 
    state.settings[`${conn.id}Integration` as keyof IntegrationSettings] === true
  );

export const selectUnresolvedAlerts = (state: IntegrationState) =>
  state.alerts.filter(alert => !alert.resolved);

export const selectRecentActivities = (state: IntegrationState, limit = 10) =>
  state.activities.slice(0, limit);

export const selectConnectionHealth = (state: IntegrationState) => {
  const connections = Object.values(state.connections);
  const connected = connections.filter(c => c.status === 'connected').length;
  return (connected / connections.length) * 100;
};