import { apiClient } from './client';
import { AxiosResponse } from 'axios';

// Types
export interface AMNAStatus {
  status: 'active' | 'idle' | 'processing' | 'error';
  lastActive: Date;
  version: string;
  uptime: number;
}

export interface AMNAMetrics {
  intelligenceLevel: string;
  queriesToday: number;
  accuracy: string;
  responseTime: number;
  contextSources: number;
  successRate: number;
  errorRate: number;
}

export interface AMNAInsight {
  id: string;
  type: 'recommendation' | 'alert' | 'achievement' | 'trend';
  source: string;
  title: string;
  description: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AMNARecommendation {
  id: string;
  title: string;
  description: string;
  category: 'automation' | 'optimization' | 'insight' | 'workflow';
  impact: number;
  effort: 'low' | 'medium' | 'high';
  implemented: boolean;
  actions: string[];
  createdAt: Date;
  implementedAt?: Date;
}

export interface AMNAAutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: string;
  actions: string[];
  enabled: boolean;
  lastRun?: Date;
  runCount: number;
  category: 'email' | 'research' | 'training' | 'vendor' | 'general';
  createdAt: Date;
  updatedAt: Date;
}

export interface AMNAContextData {
  source: string;
  dataPoints: number;
  lastSync: Date;
  quality: number;
  active: boolean;
  categories: string[];
}

export interface AMNAPerformanceData {
  responseTime: Array<{ time: string; value: number }>;
  queryVolume: Array<{ day: string; queries: number; successful: number; failed: number }>;
  contextQuality: Array<{ source: string; quality: number; relevance: number }>;
  intelligenceDistribution: Array<{ name: string; value: number }>;
}

export interface AMNAQueryRequest {
  query: string;
  context?: Record<string, any>;
  applications?: string[];
  includeHistory?: boolean;
}

export interface AMNAQueryResponse {
  response: string;
  sources: string[];
  confidence: number;
  suggestions?: string[];
  relatedQueries?: string[];
  executionTime: number;
}

// API Functions
export const amnaAPI = {
  // Status and Health
  async getStatus(): Promise<AMNAStatus> {
    const response = await apiClient.get<AMNAStatus>('/integration/amna/status');
    return response.data;
  },

  async getHealth(): Promise<boolean> {
    try {
      const response = await apiClient.get('/integration/amna/health');
      return response.status === 200;
    } catch {
      return false;
    }
  },

  // Metrics and Performance
  async getMetrics(timeRange?: string): Promise<AMNAMetrics> {
    const response = await apiClient.get<AMNAMetrics>('/integration/amna/metrics', {
      params: { timeRange }
    });
    return response.data;
  },

  async getPerformanceData(timeRange?: string): Promise<AMNAPerformanceData> {
    const response = await apiClient.get<AMNAPerformanceData>('/integration/amna/performance', {
      params: { timeRange }
    });
    return response.data;
  },

  // Insights and Recommendations
  async getInsights(filter?: { 
    type?: string; 
    source?: string; 
    priority?: string;
    limit?: number;
  }): Promise<AMNAInsight[]> {
    const response = await apiClient.get<AMNAInsight[]>('/integration/amna/insights', {
      params: filter
    });
    return response.data;
  },

  async getRecommendations(filter?: {
    category?: string;
    implemented?: boolean;
    minImpact?: number;
  }): Promise<AMNARecommendation[]> {
    const response = await apiClient.get<AMNARecommendation[]>('/integration/amna/recommendations', {
      params: filter
    });
    return response.data;
  },

  async implementRecommendation(id: string): Promise<AMNARecommendation> {
    const response = await apiClient.post<AMNARecommendation>(`/integration/amna/recommendations/${id}/implement`);
    return response.data;
  },

  // Automation Rules
  async getAutomationRules(): Promise<AMNAAutomationRule[]> {
    const response = await apiClient.get<AMNAAutomationRule[]>('/integration/amna/automation-rules');
    return response.data;
  },

  async updateAutomationRule(id: string, updates: Partial<AMNAAutomationRule>): Promise<AMNAAutomationRule> {
    const response = await apiClient.patch<AMNAAutomationRule>(`/integration/amna/automation-rules/${id}`, updates);
    return response.data;
  },

  async toggleAutomationRule(id: string, enabled: boolean): Promise<AMNAAutomationRule> {
    const response = await apiClient.patch<AMNAAutomationRule>(`/integration/amna/automation-rules/${id}`, { enabled });
    return response.data;
  },

  async runAutomationRule(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>(`/integration/amna/automation-rules/${id}/run`);
    return response.data;
  },

  async createAutomationRule(rule: Omit<AMNAAutomationRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<AMNAAutomationRule> {
    const response = await apiClient.post<AMNAAutomationRule>('/integration/amna/automation-rules', rule);
    return response.data;
  },

  async deleteAutomationRule(id: string): Promise<void> {
    await apiClient.delete(`/integration/amna/automation-rules/${id}`);
  },

  // Context Aggregation
  async getContextSources(): Promise<AMNAContextData[]> {
    const response = await apiClient.get<AMNAContextData[]>('/integration/amna/context/sources');
    return response.data;
  },

  async refreshContextSource(source: string): Promise<AMNAContextData> {
    const response = await apiClient.post<AMNAContextData>(`/integration/amna/context/sources/${source}/refresh`);
    return response.data;
  },

  async getContextCategories(): Promise<Array<{
    name: string;
    count: number;
    percentage: number;
    trend: 'up' | 'down' | 'stable';
  }>> {
    const response = await apiClient.get('/integration/amna/context/categories');
    return response.data;
  },

  // Queries and Intelligence
  async query(request: AMNAQueryRequest): Promise<AMNAQueryResponse> {
    const response = await apiClient.post<AMNAQueryResponse>('/integration/amna/query', request);
    return response.data;
  },

  async getQueryHistory(limit?: number): Promise<Array<{
    id: string;
    query: string;
    response: string;
    timestamp: Date;
    executionTime: number;
  }>> {
    const response = await apiClient.get('/integration/amna/query/history', {
      params: { limit }
    });
    return response.data;
  },

  // Training and Configuration
  async trainModel(data: {
    type: 'feedback' | 'correction' | 'example';
    input: string;
    expectedOutput?: string;
    metadata?: Record<string, any>;
  }): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post('/integration/amna/train', data);
    return response.data;
  },

  async getConfiguration(): Promise<Record<string, any>> {
    const response = await apiClient.get('/integration/amna/config');
    return response.data;
  },

  async updateConfiguration(config: Record<string, any>): Promise<Record<string, any>> {
    const response = await apiClient.patch('/integration/amna/config', config);
    return response.data;
  },

  // WebSocket Events
  subscribeToUpdates(callbacks: {
    onInsight?: (insight: AMNAInsight) => void;
    onMetricUpdate?: (metrics: AMNAMetrics) => void;
    onStatusChange?: (status: AMNAStatus) => void;
    onAutomationRun?: (rule: AMNAAutomationRule) => void;
    onError?: (error: any) => void;
  }): () => void {
    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000'}/amna`);
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'insight':
            callbacks.onInsight?.(data.payload);
            break;
          case 'metrics':
            callbacks.onMetricUpdate?.(data.payload);
            break;
          case 'status':
            callbacks.onStatusChange?.(data.payload);
            break;
          case 'automation':
            callbacks.onAutomationRun?.(data.payload);
            break;
        }
      } catch (error) {
        callbacks.onError?.(error);
      }
    };

    ws.onerror = (error) => {
      callbacks.onError?.(error);
    };

    // Return cleanup function
    return () => {
      ws.close();
    };
  }
};