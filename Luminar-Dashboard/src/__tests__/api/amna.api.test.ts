import axios from 'axios';
import { amnaAPI } from '@/lib/api/amna.api';
import { apiClient } from '@/lib/api/client';

// Mock the API client
jest.mock('@/lib/api/client');

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('amnaAPI', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Status and Health', () => {
    it('should get AMNA status', async () => {
      const mockStatus = {
        status: 'active' as const,
        lastActive: new Date(),
        version: '2.0.0',
        uptime: 86400,
      };

      mockApiClient.get.mockResolvedValueOnce({ data: mockStatus });

      const result = await amnaAPI.getStatus();

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/status');
      expect(result).toEqual(mockStatus);
    });

    it('should check health status', async () => {
      mockApiClient.get.mockResolvedValueOnce({ status: 200 });

      const result = await amnaAPI.getHealth();

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/health');
      expect(result).toBe(true);
    });

    it('should return false for health check failure', async () => {
      mockApiClient.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await amnaAPI.getHealth();

      expect(result).toBe(false);
    });
  });

  describe('Metrics and Performance', () => {
    it('should get metrics with time range', async () => {
      const mockMetrics = {
        intelligenceLevel: 'Advanced',
        queriesToday: 1247,
        accuracy: '94.3%',
        responseTime: 178,
        contextSources: 5,
        successRate: 96.4,
        errorRate: 3.6,
      };

      mockApiClient.get.mockResolvedValueOnce({ data: mockMetrics });

      const result = await amnaAPI.getMetrics('7d');

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/metrics', {
        params: { timeRange: '7d' },
      });
      expect(result).toEqual(mockMetrics);
    });

    it('should get performance data', async () => {
      const mockPerformanceData = {
        responseTime: [],
        queryVolume: [],
        contextQuality: [],
        intelligenceDistribution: [],
      };

      mockApiClient.get.mockResolvedValueOnce({ data: mockPerformanceData });

      const result = await amnaAPI.getPerformanceData('30d');

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/performance', {
        params: { timeRange: '30d' },
      });
      expect(result).toEqual(mockPerformanceData);
    });
  });

  describe('Insights and Recommendations', () => {
    it('should get insights with filters', async () => {
      const mockInsights = [
        {
          id: '1',
          type: 'recommendation',
          source: 'AMNA',
          title: 'Test Insight',
          description: 'Test description',
          category: 'test',
          priority: 'high',
          timestamp: new Date(),
        },
      ];

      mockApiClient.get.mockResolvedValueOnce({ data: mockInsights });

      const filter = { type: 'recommendation', limit: 10 };
      const result = await amnaAPI.getInsights(filter);

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/insights', {
        params: filter,
      });
      expect(result).toEqual(mockInsights);
    });

    it('should implement recommendation', async () => {
      const mockRecommendation = {
        id: '1',
        title: 'Test',
        implemented: true,
      };

      mockApiClient.post.mockResolvedValueOnce({ data: mockRecommendation });

      const result = await amnaAPI.implementRecommendation('1');

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/integration/amna/recommendations/1/implement'
      );
      expect(result).toEqual(mockRecommendation);
    });
  });

  describe('Automation Rules', () => {
    it('should get automation rules', async () => {
      const mockRules = [
        {
          id: '1',
          name: 'Test Rule',
          enabled: true,
        },
      ];

      mockApiClient.get.mockResolvedValueOnce({ data: mockRules });

      const result = await amnaAPI.getAutomationRules();

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/automation-rules');
      expect(result).toEqual(mockRules);
    });

    it('should toggle automation rule', async () => {
      const mockRule = {
        id: '1',
        name: 'Test Rule',
        enabled: false,
      };

      mockApiClient.patch.mockResolvedValueOnce({ data: mockRule });

      const result = await amnaAPI.toggleAutomationRule('1', false);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/integration/amna/automation-rules/1',
        { enabled: false }
      );
      expect(result).toEqual(mockRule);
    });

    it('should run automation rule', async () => {
      const mockResponse = { success: true, message: 'Rule executed' };

      mockApiClient.post.mockResolvedValueOnce({ data: mockResponse });

      const result = await amnaAPI.runAutomationRule('1');

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/integration/amna/automation-rules/1/run'
      );
      expect(result).toEqual(mockResponse);
    });

    it('should create automation rule', async () => {
      const newRule = {
        name: 'New Rule',
        description: 'Test',
        trigger: 'Test trigger',
        actions: ['Action 1'],
        enabled: true,
        category: 'general' as const,
      };

      const mockCreatedRule = {
        ...newRule,
        id: '2',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockApiClient.post.mockResolvedValueOnce({ data: mockCreatedRule });

      const result = await amnaAPI.createAutomationRule(newRule);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/integration/amna/automation-rules',
        newRule
      );
      expect(result).toEqual(mockCreatedRule);
    });

    it('should delete automation rule', async () => {
      mockApiClient.delete.mockResolvedValueOnce({});

      await amnaAPI.deleteAutomationRule('1');

      expect(mockApiClient.delete).toHaveBeenCalledWith(
        '/integration/amna/automation-rules/1'
      );
    });
  });

  describe('Query Operations', () => {
    it('should execute query', async () => {
      const queryRequest = {
        query: 'Test query',
        context: { app: 'test' },
      };

      const mockResponse = {
        response: 'Test response',
        sources: ['source1'],
        confidence: 0.95,
        executionTime: 150,
      };

      mockApiClient.post.mockResolvedValueOnce({ data: mockResponse });

      const result = await amnaAPI.query(queryRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/integration/amna/query',
        queryRequest
      );
      expect(result).toEqual(mockResponse);
    });

    it('should get query history', async () => {
      const mockHistory = [
        {
          id: '1',
          query: 'Test query',
          response: 'Test response',
          timestamp: new Date(),
          executionTime: 150,
        },
      ];

      mockApiClient.get.mockResolvedValueOnce({ data: mockHistory });

      const result = await amnaAPI.getQueryHistory(10);

      expect(mockApiClient.get).toHaveBeenCalledWith('/integration/amna/query/history', {
        params: { limit: 10 },
      });
      expect(result).toEqual(mockHistory);
    });
  });

  describe('WebSocket Subscription', () => {
    let mockWebSocket: any;

    beforeEach(() => {
      mockWebSocket = {
        readyState: WebSocket.OPEN,
        send: jest.fn(),
        close: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      };

      global.WebSocket = jest.fn(() => mockWebSocket) as any;
    });

    it('should subscribe to WebSocket updates', () => {
      const callbacks = {
        onInsight: jest.fn(),
        onMetricUpdate: jest.fn(),
        onStatusChange: jest.fn(),
        onAutomationRun: jest.fn(),
        onError: jest.fn(),
      };

      const unsubscribe = amnaAPI.subscribeToUpdates(callbacks);

      // Simulate WebSocket message
      const messageHandler = mockWebSocket.onmessage;
      if (messageHandler) {
        messageHandler({
          data: JSON.stringify({
            type: 'insight',
            payload: { title: 'Test insight' },
          }),
        });
      }

      expect(callbacks.onInsight).toHaveBeenCalledWith({ title: 'Test insight' });

      // Test cleanup
      unsubscribe();
      expect(mockWebSocket.close).toHaveBeenCalled();
    });

    it('should handle WebSocket errors', () => {
      const callbacks = {
        onError: jest.fn(),
      };

      amnaAPI.subscribeToUpdates(callbacks);

      // Simulate error
      const errorHandler = mockWebSocket.onerror;
      if (errorHandler) {
        const error = new Error('WebSocket error');
        errorHandler(error);
      }

      expect(callbacks.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});