import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ConnectionStatus from '@/components/integration/amna/ConnectionStatus';
import { useAMNAWebSocket } from '@/components/integration/amna/AMNAWebSocketProvider';

// Mock the WebSocket hook
jest.mock('@/components/integration/amna/AMNAWebSocketProvider');

const mockUseAMNAWebSocket = useAMNAWebSocket as jest.MockedFunction<typeof useAMNAWebSocket>;

describe('ConnectionStatus', () => {
  const mockReconnect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show connected status when connected', () => {
    mockUseAMNAWebSocket.mockReturnValue({
      isConnected: true,
      lastMessage: null,
      sendMessage: jest.fn(),
      reconnect: mockReconnect,
    });

    render(<ConnectionStatus />);

    expect(screen.getByText('Connected')).toBeInTheDocument();
    expect(screen.queryByText('Reconnect')).not.toBeInTheDocument();
    
    // Check for the pulse indicator
    const badge = screen.getByText('Connected').closest('[class*="badge"]');
    expect(badge).toHaveClass('bg-green-100');
  });

  it('should show disconnected status and reconnect button when disconnected', () => {
    mockUseAMNAWebSocket.mockReturnValue({
      isConnected: false,
      lastMessage: null,
      sendMessage: jest.fn(),
      reconnect: mockReconnect,
    });

    render(<ConnectionStatus />);

    expect(screen.getByText('Disconnected')).toBeInTheDocument();
    expect(screen.getByText('Reconnect')).toBeInTheDocument();
  });

  it('should call reconnect when reconnect button is clicked', () => {
    mockUseAMNAWebSocket.mockReturnValue({
      isConnected: false,
      lastMessage: null,
      sendMessage: jest.fn(),
      reconnect: mockReconnect,
    });

    render(<ConnectionStatus />);

    const reconnectButton = screen.getByText('Reconnect');
    fireEvent.click(reconnectButton);

    expect(mockReconnect).toHaveBeenCalledTimes(1);
  });

  it('should have appropriate styling for connected state', () => {
    mockUseAMNAWebSocket.mockReturnValue({
      isConnected: true,
      lastMessage: null,
      sendMessage: jest.fn(),
      reconnect: mockReconnect,
    });

    render(<ConnectionStatus />);

    const badge = screen.getByText('Connected').closest('[class*="badge"]');
    expect(badge).toHaveClass('bg-green-100', 'text-green-700');
  });

  it('should have appropriate styling for disconnected state', () => {
    mockUseAMNAWebSocket.mockReturnValue({
      isConnected: false,
      lastMessage: null,
      sendMessage: jest.fn(),
      reconnect: mockReconnect,
    });

    render(<ConnectionStatus />);

    const badge = screen.getByText('Disconnected').closest('[class*="badge"]');
    expect(badge).toHaveClass('destructive');
  });
});