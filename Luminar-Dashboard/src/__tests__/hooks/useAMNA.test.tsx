import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { act } from 'react-dom/test-utils';
import {
  useAMNAStatus,
  useAMNAMetrics,
  useAMNAInsights,
  useAMNARecommendations,
  useImplementRecommendation,
  useAMNAAutomationRules,
  useToggleAutomationRule,
} from '@/hooks/useAMNA';
import { amnaAPI } from '@/lib/api/amna.api';

// Mock the API
jest.mock('@/lib/api/amna.api');
jest.mock('@/store/integration/integrationStore');

// Mock toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockAmnaAPI = amnaAPI as jest.Mocked<typeof amnaAPI>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useAMNA hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useAMNAStatus', () => {
    it('should fetch AMNA status successfully', async () => {
      const mockStatus = {
        status: 'active' as const,
        lastActive: new Date(),
        version: '2.0.0',
        uptime: 86400,
      };

      mockAmnaAPI.getStatus.mockResolvedValueOnce(mockStatus);

      const { result } = renderHook(() => useAMNAStatus(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockStatus);
      expect(mockAmnaAPI.getStatus).toHaveBeenCalledTimes(1);
    });

    it('should handle status fetch error', async () => {
      const error = new Error('Network error');
      mockAmnaAPI.getStatus.mockRejectedValueOnce(error);

      const { result } = renderHook(() => useAMNAStatus(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeDefined();
    });
  });

  describe('useAMNAMetrics', () => {
    it('should fetch metrics with time range', async () => {
      const mockMetrics = {
        intelligenceLevel: 'Advanced',
        queriesToday: 1247,
        accuracy: '94.3%',
        responseTime: 178,
        contextSources: 5,
        successRate: 96.4,
        errorRate: 3.6,
      };

      mockAmnaAPI.getMetrics.mockResolvedValueOnce(mockMetrics);

      const { result } = renderHook(() => useAMNAMetrics('7d'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockMetrics);
      expect(mockAmnaAPI.getMetrics).toHaveBeenCalledWith('7d');
    });
  });

  describe('useAMNAInsights', () => {
    it('should fetch insights with filters', async () => {
      const mockInsights = [
        {
          id: '1',
          type: 'recommendation' as const,
          source: 'E-Connect',
          title: 'Test Insight',
          description: 'Test description',
          category: 'test',
          priority: 'high' as const,
          timestamp: new Date(),
        },
      ];

      mockAmnaAPI.getInsights.mockResolvedValueOnce(mockInsights);

      const filter = { type: 'recommendation', limit: 10 };
      const { result } = renderHook(() => useAMNAInsights(filter), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockInsights);
      expect(mockAmnaAPI.getInsights).toHaveBeenCalledWith(filter);
    });
  });

  describe('useImplementRecommendation', () => {
    it('should implement recommendation successfully', async () => {
      const mockRecommendation = {
        id: '1',
        title: 'Test Recommendation',
        description: 'Test',
        category: 'automation' as const,
        impact: 85,
        effort: 'medium' as const,
        implemented: true,
        actions: [],
        createdAt: new Date(),
        implementedAt: new Date(),
      };

      mockAmnaAPI.implementRecommendation.mockResolvedValueOnce(mockRecommendation);

      const { result } = renderHook(() => useImplementRecommendation(), {
        wrapper: createWrapper(),
      });

      await act(async () => {
        await result.current.mutateAsync('1');
      });

      expect(mockAmnaAPI.implementRecommendation).toHaveBeenCalledWith('1');
      expect(result.current.isSuccess).toBe(true);
    });
  });

  describe('useToggleAutomationRule', () => {
    it('should toggle automation rule', async () => {
      const mockRule = {
        id: '1',
        name: 'Test Rule',
        description: 'Test',
        trigger: 'Test trigger',
        actions: [],
        enabled: true,
        runCount: 0,
        category: 'general' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAmnaAPI.toggleAutomationRule.mockResolvedValueOnce(mockRule);

      const { result } = renderHook(() => useToggleAutomationRule(), {
        wrapper: createWrapper(),
      });

      await act(async () => {
        await result.current.mutateAsync({ id: '1', enabled: true });
      });

      expect(mockAmnaAPI.toggleAutomationRule).toHaveBeenCalledWith('1', true);
      expect(result.current.isSuccess).toBe(true);
    });

    it('should handle toggle error', async () => {
      const error = new Error('Failed to toggle');
      mockAmnaAPI.toggleAutomationRule.mockRejectedValueOnce(error);

      const { result } = renderHook(() => useToggleAutomationRule(), {
        wrapper: createWrapper(),
      });

      await act(async () => {
        try {
          await result.current.mutateAsync({ id: '1', enabled: true });
        } catch (e) {
          expect(e).toBe(error);
        }
      });

      expect(result.current.isError).toBe(true);
    });
  });
});