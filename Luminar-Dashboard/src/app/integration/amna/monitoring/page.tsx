'use client';

import React from 'react';
import { 
  Brain, 
  Activity,
  BarChart3,
  Layers,
  ArrowLeft
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load monitoring components
const RealTimeActivityFeed = dynamic(
  () => import('@/components/integration/amna/RealTimeActivityFeed'),
  { 
    loading: () => <Skeleton className="h-[500px] w-full" />,
    ssr: false 
  }
);

const SystemHealthMonitor = dynamic(
  () => import('@/components/integration/amna/SystemHealthMonitor'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const QueryAnalytics = dynamic(
  () => import('@/components/integration/amna/QueryAnalytics'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const IntelligenceHeatmap = dynamic(
  () => import('@/components/integration/amna/IntelligenceHeatmap'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

export default function AMNAMonitoringPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Link href="/integration/amna">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
            <Activity className="w-8 h-8 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AMNA Monitoring Center</h1>
            <p className="text-muted-foreground">
              Real-time monitoring and analytics for AMNA intelligence operations
            </p>
          </div>
        </div>
      </div>

      {/* Monitoring Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-Time Activity Feed */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="lg:row-span-2"
        >
          <RealTimeActivityFeed />
        </motion.div>

        {/* System Health Monitor */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <SystemHealthMonitor />
        </motion.div>

        {/* Query Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="lg:col-span-2"
        >
          <QueryAnalytics />
        </motion.div>

        {/* Intelligence Heatmap */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="lg:col-span-2"
        >
          <IntelligenceHeatmap />
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mt-8 p-6 bg-muted/50 rounded-lg"
      >
        <h3 className="font-semibold mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
          <Button variant="outline" size="sm">
            <Layers className="w-4 h-4 mr-2" />
            Configure Alerts
          </Button>
          <Button variant="outline" size="sm">
            <Brain className="w-4 h-4 mr-2" />
            Optimize Performance
          </Button>
        </div>
      </motion.div>
    </div>
  );
}