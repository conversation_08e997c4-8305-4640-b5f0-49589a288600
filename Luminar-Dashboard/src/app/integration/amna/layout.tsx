import { AMNAWebSocketProvider } from '@/components/integration/amna/AMNAWebSocketProvider';
import AMNAErrorBoundary from '@/components/integration/amna/AMNAErrorBoundary';

export default function AMNALayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AMNAErrorBoundary>
      <AMNAWebSocketProvider>
        {children}
      </AMNAWebSocketProvider>
    </AMNAErrorBoundary>
  );
}