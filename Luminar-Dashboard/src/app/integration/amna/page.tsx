'use client';

import React from 'react';
import { 
  Brain, 
  <PERSON>rkles, 
  TrendingUp, 
  Zap, 
  Activity,
  MessageSquare,
  Target,
  BarChart3,
  Network,
  Bot,
  AlertCircle,
  Loader2,
  MonitorDot,
  Settings
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import dynamic from 'next/dynamic';
import ConnectionStatus from '@/components/integration/amna/ConnectionStatus';

// Lazy load heavy components for better performance
const CrossApplicationInsights = dynamic(
  () => import('@/components/integration/amna/CrossApplicationInsights'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const IntelligenceRecommendations = dynamic(
  () => import('@/components/integration/amna/IntelligenceRecommendations'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const AutomationRules = dynamic(
  () => import('@/components/integration/amna/AutomationRules'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const ContextAggregation = dynamic(
  () => import('@/components/integration/amna/ContextAggregation'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);

const AMNAPerformanceMetrics = dynamic(
  () => import('@/components/integration/amna/AMNAPerformanceMetrics'),
  { 
    loading: () => <Skeleton className="h-96 w-full" />,
    ssr: false 
  }
);
import { useIntegrationStore } from '@/store/integration/integrationStore';
import { useAMNADashboard } from '@/hooks/useAMNA';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useMediaQuery } from '@/hooks/useMediaQuery';

export default function AMNADashboardPage() {
  const { amnaStatus: storeStatus, amnaMetrics: storeMetrics } = useIntegrationStore();
  const { status, metrics, isLoading, error } = useAMNADashboard();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');

  // Use API data with fallback to store data
  const amnaStatus = status.data?.status || storeStatus || 'idle';
  const amnaMetrics = metrics.data || storeMetrics;

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load AMNA dashboard. Please check your connection and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className={`flex ${isMobile ? 'flex-col' : 'items-center'} gap-4 mb-4`}>
          <div className="flex items-center gap-4">
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
              <Brain className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} text-purple-600 dark:text-purple-400`} />
            </div>
            <div className="flex-1">
              <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold`}>AMNA Intelligence Center</h1>
              {!isMobile && (
                <p className="text-muted-foreground">
                  Advanced Multi-Modal Neural Assistant - Central Intelligence Hub
                </p>
              )}
            </div>
          </div>
          <div className={`flex items-center gap-2 ${isMobile ? 'w-full' : 'ml-auto'}`}>
            <Link href="/integration/amna/monitoring" className="flex-1 sm:flex-auto">
              <Button variant="outline" size={isMobile ? "default" : "sm"} className="w-full sm:w-auto">
                <MonitorDot className="w-4 h-4 mr-2" />
                {isMobile ? 'Monitor' : 'Monitoring Center'}
              </Button>
            </Link>
            <Button variant="outline" size={isMobile ? "default" : "sm"} className="flex-1 sm:flex-auto">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Status Bar */}
        {isLoading ? (
          <Skeleton className={`${isMobile ? 'h-40' : 'h-20'} w-full`} />
        ) : (
          <div className={`${
            isMobile 
              ? 'grid grid-cols-2 gap-3' 
              : 'flex items-center gap-6'
          } p-4 bg-muted/50 rounded-lg`}>
            <div className="flex items-center gap-2">
              <Activity className={`w-5 h-5 ${amnaStatus === 'active' ? 'text-green-500' : amnaStatus === 'processing' ? 'animate-pulse text-yellow-500' : 'text-gray-400'}`} />
              <span className="text-sm font-medium">
                Status: <span className={amnaStatus === 'active' ? 'text-green-500' : amnaStatus === 'processing' ? 'text-yellow-500' : 'text-gray-500'}>
                  {amnaStatus.charAt(0).toUpperCase() + amnaStatus.slice(1)}
                </span>
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              <span className="text-sm">
                {isMobile ? 'Level:' : 'Intelligence Level:'} <span className="font-medium">{amnaMetrics?.intelligenceLevel || 'Advanced'}</span>
              </span>
            </div>
            <div className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              <span className="text-sm">
                {isMobile ? 'Queries:' : 'Queries Today:'} <span className="font-medium">{amnaMetrics?.queriesToday?.toLocaleString() || '0'}</span>
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="w-5 h-5 text-purple-500" />
              <span className="text-sm">
                Accuracy: <span className="font-medium">{amnaMetrics?.accuracy || '0%'}</span>
              </span>
            </div>
            {amnaStatus === 'processing' && !isMobile && (
              <div className="ml-auto flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Processing query...</span>
              </div>
            )}
            {!isMobile && (
              <div className="ml-auto">
                <ConnectionStatus />
              </div>
            )}
            {isMobile && (
              <div className="col-span-2 flex justify-between items-center">
                {amnaStatus === 'processing' && (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Processing...</span>
                  </div>
                )}
                <div className="ml-auto">
                  <ConnectionStatus />
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Main Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Cross-Application Insights */}
        <div className="lg:col-span-2 space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CrossApplicationInsights />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <ContextAggregation />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <AMNAPerformanceMetrics />
          </motion.div>
        </div>

        {/* Right Column - Recommendations & Automation */}
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <IntelligenceRecommendations />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <AutomationRules />
          </motion.div>
        </div>
      </div>
    </div>
  );
}