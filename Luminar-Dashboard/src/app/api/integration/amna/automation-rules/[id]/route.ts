import { NextResponse } from 'next/server';

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  const body = await request.json();
  const { id } = params;
  
  // Mock update - in production, this would update in database
  return NextResponse.json({
    id,
    name: 'Updated Rule',
    description: 'Mock updated rule',
    trigger: 'Updated trigger',
    actions: ['Action 1', 'Action 2'],
    enabled: body.enabled ?? true,
    lastRun: new Date(),
    runCount: 100,
    category: 'general',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
  });
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  
  // Mock run automation
  return NextResponse.json({
    success: true,
    message: `Automation rule ${id} executed successfully`
  });
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  
  // Mock delete
  return NextResponse.json({ success: true });
}