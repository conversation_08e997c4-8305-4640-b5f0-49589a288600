import { NextResponse } from 'next/server';

const mockRules = [
  {
    id: '1',
    name: 'Auto-Tag Important Emails',
    description: 'Automatically tag and prioritize emails based on content and sender',
    trigger: 'New email received',
    actions: ['Analyze content', 'Apply tags', 'Update priority'],
    enabled: true,
    lastRun: new Date(Date.now() - 10 * 60 * 1000),
    runCount: 324,
    category: 'email',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 10 * 60 * 1000),
  },
  {
    id: '2',
    name: 'Research to Training Pipeline',
    description: 'Convert research insights into training recommendations',
    trigger: 'New research completed',
    actions: ['Extract key findings', 'Identify skill gaps', 'Create training plan'],
    enabled: true,
    lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
    runCount: 48,
    category: 'research',
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
  },
  {
    id: '3',
    name: 'Vendor Performance Alerts',
    description: 'Alert when vendor metrics fall below thresholds',
    trigger: 'Performance threshold breach',
    actions: ['Send notification', 'Create report', 'Schedule review'],
    enabled: false,
    lastRun: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    runCount: 12,
    category: 'vendor',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
  },
  {
    id: '4',
    name: 'Weekly Achievement Capture',
    description: 'Automatically capture and categorize weekly achievements',
    trigger: 'Weekly schedule (Friday 4pm)',
    actions: ['Collect activities', 'Categorize achievements', 'Generate summary'],
    enabled: true,
    lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    runCount: 156,
    category: 'general',
    createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
  }
];

export async function GET() {
  return NextResponse.json(mockRules);
}

export async function POST(request: Request) {
  const body = await request.json();
  const newRule = {
    ...body,
    id: String(Date.now()),
    runCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  return NextResponse.json(newRule);
}