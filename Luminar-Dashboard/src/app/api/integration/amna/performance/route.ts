import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const timeRange = searchParams.get('timeRange') || '7d';
  
  // Mock data - adjust based on timeRange if needed
  return NextResponse.json({
    responseTime: [
      { time: '00:00', value: 145 },
      { time: '04:00', value: 132 },
      { time: '08:00', value: 189 },
      { time: '12:00', value: 234 },
      { time: '16:00', value: 198 },
      { time: '20:00', value: 167 },
      { time: '24:00', value: 143 }
    ],
    queryVolume: [
      { day: 'Mon', queries: 1245, successful: 1198, failed: 47 },
      { day: 'Tue', queries: 1389, successful: 1342, failed: 47 },
      { day: 'Wed', queries: 1456, successful: 1398, failed: 58 },
      { day: 'Thu', queries: 1523, successful: 1476, failed: 47 },
      { day: 'Fri', queries: 1678, successful: 1634, failed: 44 },
      { day: 'Sat', queries: 892, successful: 867, failed: 25 },
      { day: 'Sun', queries: 945, successful: 923, failed: 22 }
    ],
    contextQuality: [
      { source: 'E-Connect', quality: 94, relevance: 89 },
      { source: 'Lighthouse', quality: 89, relevance: 92 },
      { source: 'Training', quality: 91, relevance: 87 },
      { source: 'Vendors', quality: 87, relevance: 85 },
      { source: 'Wins', quality: 95, relevance: 90 }
    ],
    intelligenceDistribution: [
      { name: 'Basic Queries', value: 35, color: '#8884d8' },
      { name: 'Complex Analysis', value: 30, color: '#82ca9d' },
      { name: 'Predictions', value: 20, color: '#ffc658' },
      { name: 'Recommendations', value: 15, color: '#ff8042' }
    ]
  });
}