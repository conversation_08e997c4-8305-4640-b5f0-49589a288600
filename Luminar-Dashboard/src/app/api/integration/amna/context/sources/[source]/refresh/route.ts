import { NextResponse } from 'next/server';

export async function POST(
  request: Request,
  { params }: { params: { source: string } }
) {
  const { source } = params;
  
  // Mock refresh - in production, this would trigger actual data sync
  return NextResponse.json({
    source,
    dataPoints: Math.floor(Math.random() * 10000) + 1000,
    lastSync: new Date(),
    quality: Math.floor(Math.random() * 20) + 80,
    active: true,
    categories: ['Category 1', 'Category 2', 'Category 3']
  });
}