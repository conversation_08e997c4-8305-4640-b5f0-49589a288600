import { NextResponse } from 'next/server';

export async function GET() {
  // Mock data for development
  return NextResponse.json([
    {
      source: 'E-Connect',
      dataPoints: 12847,
      lastSync: new Date(Date.now() - 2 * 60 * 1000),
      quality: 94,
      active: true,
      categories: ['Communications', 'Contacts', 'Campaigns']
    },
    {
      source: 'Lighthouse',
      dataPoints: 3456,
      lastSync: new Date(Date.now() - 5 * 60 * 1000),
      quality: 89,
      active: true,
      categories: ['Research', 'Insights', 'Market Data']
    },
    {
      source: 'Training',
      dataPoints: 892,
      lastSync: new Date(Date.now() - 15 * 60 * 1000),
      quality: 91,
      active: true,
      categories: ['Skills', 'Assessments', 'Progress']
    },
    {
      source: 'Vendors',
      dataPoints: 567,
      lastSync: new Date(Date.now() - 30 * 60 * 1000),
      quality: 87,
      active: true,
      categories: ['Contracts', 'Performance', 'Capabilities']
    },
    {
      source: 'Wins',
      dataPoints: 234,
      lastSync: new Date(Date.now() - 60 * 60 * 1000),
      quality: 95,
      active: true,
      categories: ['Achievements', 'Milestones', 'Metrics']
    }
  ]);
}