import { NextResponse } from 'next/server';

export async function GET() {
  // Mock data for development
  return NextResponse.json([
    {
      id: '1',
      title: 'Automate Training-Vendor Matching',
      description: 'AI can automatically match training needs with vendor capabilities based on historical data',
      category: 'automation',
      impact: 85,
      effort: 'medium',
      implemented: false,
      actions: ['Enable auto-matching', 'Configure parameters', 'Review matches'],
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    },
    {
      id: '2',
      title: 'Optimize Email Response Suggestions',
      description: 'Improve response time by 40% with context-aware email suggestions',
      category: 'optimization',
      impact: 72,
      effort: 'low',
      implemented: false,
      actions: ['Enable suggestions', 'Train on your data'],
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    },
    {
      id: '3',
      title: 'Cross-Reference Research Insights',
      description: 'Automatically correlate Lighthouse research with vendor performance data',
      category: 'insight',
      impact: 68,
      effort: 'low',
      implemented: true,
      actions: ['View insights', 'Adjust parameters'],
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      implementedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    },
    {
      id: '4',
      title: 'Weekly Achievement Automation',
      description: 'Auto-capture and categorize achievements from all applications',
      category: 'workflow',
      impact: 60,
      effort: 'medium',
      implemented: false,
      actions: ['Configure rules', 'Set categories', 'Enable automation'],
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    }
  ]);
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Mock implementation endpoint
  const { id } = params;
  
  return NextResponse.json({
    id,
    title: 'Recommendation',
    description: 'Mock implementation',
    category: 'automation',
    impact: 85,
    effort: 'medium',
    implemented: true,
    actions: [],
    createdAt: new Date(),
    implementedAt: new Date()
  });
}