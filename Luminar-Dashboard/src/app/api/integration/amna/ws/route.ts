// Mock WebSocket endpoint for development
// In production, this would be handled by the Command-Center backend

import { NextResponse } from 'next/server';

// This is a placeholder - actual WebSocket implementation would be in Command-Center
export async function GET() {
  return NextResponse.json({
    message: 'WebSocket endpoint is handled by Command-Center backend',
    wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000/amna'
  });
}

// Mock function to simulate WebSocket messages (for development)
export function generateMockWebSocketMessage() {
  const messageTypes = [
    {
      type: 'activity',
      payload: {
        type: 'query',
        title: 'User query processed',
        description: 'Analyzed cross-application data for performance insights',
        category: 'analysis',
        metadata: {
          executionTime: Math.floor(Math.random() * 300) + 100,
          applications: ['E-Connect', 'Lighthouse'],
          status: 'success'
        }
      }
    },
    {
      type: 'insight',
      payload: {
        title: 'Training opportunity detected',
        description: 'Based on recent queries, team members could benefit from advanced data analysis training',
        category: 'recommendation',
        priority: 'high',
        source: 'AMNA Intelligence'
      }
    },
    {
      type: 'metric',
      payload: {
        responseTime: Math.floor(Math.random() * 200) + 100,
        successRate: Math.floor(Math.random() * 5) + 95,
        queriesToday: Math.floor(Math.random() * 100) + 1000,
        contextSources: 5
      }
    },
    {
      type: 'alert',
      payload: {
        severity: 'warning',
        title: 'High response time detected',
        message: 'Response times have increased by 25% in the last hour'
      }
    }
  ];

  return messageTypes[Math.floor(Math.random() * messageTypes.length)];
}