import { NextResponse } from 'next/server';

export async function GET() {
  // Mock data for development
  return NextResponse.json([
    {
      id: '1',
      type: 'recommendation',
      source: 'E-Connect + Training',
      title: 'Training Opportunity Identified',
      description: 'Based on recent email communications, 3 team members would benefit from advanced Excel training',
      category: 'training',
      priority: 'high',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      metadata: {
        impact: 'high',
        users: ['<PERSON>', '<PERSON>', '<PERSON>'],
        skill: 'Excel',
        level: 'Advanced'
      }
    },
    {
      id: '2',
      type: 'alert',
      source: 'Vendors + Lighthouse',
      title: 'Vendor Performance Concern',
      description: 'Research indicates potential issues with vendor delivery times based on industry trends',
      category: 'vendor',
      priority: 'medium',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      metadata: {
        impact: 'medium',
        vendor: 'TechSupplies Inc',
        metric: 'delivery_time',
        variance: '+23%'
      }
    },
    {
      id: '3',
      type: 'achievement',
      source: 'All Applications',
      title: 'Productivity Milestone Reached',
      description: 'Team efficiency increased by 32% this month across all integrated applications',
      category: 'performance',
      priority: 'high',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      metadata: {
        impact: 'high',
        improvement: 32,
        applications: 5,
        timeframe: 'month'
      }
    },
    {
      id: '4',
      type: 'trend',
      source: 'Lighthouse + E-Connect',
      title: 'Emerging Market Opportunity',
      description: 'Research correlates with email patterns suggesting new market segment interest',
      category: 'market',
      priority: 'medium',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      metadata: {
        impact: 'medium',
        segment: 'Healthcare Tech',
        growth: '+15%',
        confidence: 87
      }
    }
  ]);
}