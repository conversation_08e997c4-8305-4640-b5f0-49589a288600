import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { amnaAPI } from '@/lib/api/amna.api';
import { useIntegrationStore } from '@/store/integration/integrationStore';
import { useEffect } from 'react';
import { toast } from 'react-hot-toast';

// Query Keys
const AMNA_KEYS = {
  all: ['amna'] as const,
  status: () => [...AMNA_KEYS.all, 'status'] as const,
  metrics: (timeRange?: string) => [...AMNA_KEYS.all, 'metrics', timeRange] as const,
  performance: (timeRange?: string) => [...AMNA_KEYS.all, 'performance', timeRange] as const,
  insights: (filter?: any) => [...AMNA_KEYS.all, 'insights', filter] as const,
  recommendations: (filter?: any) => [...AMNA_KEYS.all, 'recommendations', filter] as const,
  automationRules: () => [...AMNA_KEYS.all, 'automation-rules'] as const,
  contextSources: () => [...AMNA_KEYS.all, 'context-sources'] as const,
  contextCategories: () => [...AMNA_KEYS.all, 'context-categories'] as const,
  queryHistory: (limit?: number) => [...AMNA_KEYS.all, 'query-history', limit] as const,
};

// Status Hook
export function useAMNAStatus() {
  const setAmnaStatus = useIntegrationStore((state) => state.setAmnaStatus);
  
  return useQuery({
    queryKey: AMNA_KEYS.status(),
    queryFn: amnaAPI.getStatus,
    refetchInterval: 30000, // Refetch every 30 seconds
    onSuccess: (data) => {
      setAmnaStatus?.(data.status);
    },
  });
}

// Metrics Hook
export function useAMNAMetrics(timeRange?: string) {
  const setAmnaMetrics = useIntegrationStore((state) => state.setAmnaMetrics);
  
  return useQuery({
    queryKey: AMNA_KEYS.metrics(timeRange),
    queryFn: () => amnaAPI.getMetrics(timeRange),
    refetchInterval: 60000, // Refetch every minute
    onSuccess: (data) => {
      setAmnaMetrics?.(data);
    },
  });
}

// Performance Data Hook
export function useAMNAPerformance(timeRange?: string) {
  return useQuery({
    queryKey: AMNA_KEYS.performance(timeRange),
    queryFn: () => amnaAPI.getPerformanceData(timeRange),
    refetchInterval: 120000, // Refetch every 2 minutes
  });
}

// Insights Hook
export function useAMNAInsights(filter?: { 
  type?: string; 
  source?: string; 
  priority?: string;
  limit?: number;
}) {
  const setAmnaInsights = useIntegrationStore((state) => state.setAmnaInsights);
  
  return useQuery({
    queryKey: AMNA_KEYS.insights(filter),
    queryFn: () => amnaAPI.getInsights(filter),
    refetchInterval: 90000, // Refetch every 90 seconds
    onSuccess: (data) => {
      setAmnaInsights?.(data);
    },
  });
}

// Recommendations Hook
export function useAMNARecommendations(filter?: {
  category?: string;
  implemented?: boolean;
  minImpact?: number;
}) {
  const setAmnaRecommendations = useIntegrationStore((state) => state.setAmnaRecommendations);
  
  return useQuery({
    queryKey: AMNA_KEYS.recommendations(filter),
    queryFn: () => amnaAPI.getRecommendations(filter),
    onSuccess: (data) => {
      setAmnaRecommendations?.(data);
    },
  });
}

// Implement Recommendation Mutation
export function useImplementRecommendation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: amnaAPI.implementRecommendation,
    onSuccess: (data) => {
      queryClient.invalidateQueries(AMNA_KEYS.recommendations());
      toast.success(`Recommendation "${data.title}" implemented successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to implement recommendation');
    },
  });
}

// Automation Rules Hook
export function useAMNAAutomationRules() {
  const setAmnaAutomationRules = useIntegrationStore((state) => state.setAmnaAutomationRules);
  
  return useQuery({
    queryKey: AMNA_KEYS.automationRules(),
    queryFn: amnaAPI.getAutomationRules,
    onSuccess: (data) => {
      setAmnaAutomationRules?.(data);
    },
  });
}

// Toggle Automation Rule Mutation
export function useToggleAutomationRule() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, enabled }: { id: string; enabled: boolean }) => 
      amnaAPI.toggleAutomationRule(id, enabled),
    onSuccess: (data) => {
      queryClient.invalidateQueries(AMNA_KEYS.automationRules());
      toast.success(`Rule "${data.name}" ${data.enabled ? 'enabled' : 'disabled'}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to toggle automation rule');
    },
  });
}

// Run Automation Rule Mutation
export function useRunAutomationRule() {
  return useMutation({
    mutationFn: amnaAPI.runAutomationRule,
    onSuccess: (data) => {
      toast.success(data.message || 'Automation rule executed successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to run automation rule');
    },
  });
}

// Create Automation Rule Mutation
export function useCreateAutomationRule() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: amnaAPI.createAutomationRule,
    onSuccess: (data) => {
      queryClient.invalidateQueries(AMNA_KEYS.automationRules());
      toast.success(`Rule "${data.name}" created successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create automation rule');
    },
  });
}

// Delete Automation Rule Mutation
export function useDeleteAutomationRule() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: amnaAPI.deleteAutomationRule,
    onSuccess: () => {
      queryClient.invalidateQueries(AMNA_KEYS.automationRules());
      toast.success('Automation rule deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete automation rule');
    },
  });
}

// Context Sources Hook
export function useAMNAContextSources() {
  return useQuery({
    queryKey: AMNA_KEYS.contextSources(),
    queryFn: amnaAPI.getContextSources,
    refetchInterval: 180000, // Refetch every 3 minutes
  });
}

// Refresh Context Source Mutation
export function useRefreshContextSource() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: amnaAPI.refreshContextSource,
    onSuccess: (data) => {
      queryClient.invalidateQueries(AMNA_KEYS.contextSources());
      toast.success(`${data.source} context refreshed`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to refresh context source');
    },
  });
}

// Context Categories Hook
export function useAMNAContextCategories() {
  return useQuery({
    queryKey: AMNA_KEYS.contextCategories(),
    queryFn: amnaAPI.getContextCategories,
    refetchInterval: 180000, // Refetch every 3 minutes
  });
}

// Query Mutation
export function useAMNAQuery() {
  return useMutation({
    mutationFn: amnaAPI.query,
    onError: (error: any) => {
      toast.error(error.message || 'Query failed');
    },
  });
}

// Query History Hook
export function useAMNAQueryHistory(limit?: number) {
  return useQuery({
    queryKey: AMNA_KEYS.queryHistory(limit),
    queryFn: () => amnaAPI.getQueryHistory(limit),
  });
}

// WebSocket Subscription Hook
export function useAMNAWebSocket() {
  const queryClient = useQueryClient();
  const { 
    addActivity, 
    addAlert, 
    setAmnaStatus,
    setAmnaMetrics 
  } = useIntegrationStore();

  useEffect(() => {
    const unsubscribe = amnaAPI.subscribeToUpdates({
      onInsight: (insight) => {
        // Add as activity
        addActivity({
          id: insight.id,
          timestamp: new Date(insight.timestamp),
          source: 'amna',
          type: 'insight',
          title: insight.title,
          description: insight.description,
          category: insight.category,
          metadata: insight.metadata,
        });

        // Invalidate insights query to show new data
        queryClient.invalidateQueries(AMNA_KEYS.insights());
      },
      onMetricUpdate: (metrics) => {
        setAmnaMetrics?.(metrics);
        queryClient.invalidateQueries(AMNA_KEYS.metrics());
      },
      onStatusChange: (status) => {
        setAmnaStatus?.(status.status);
        queryClient.invalidateQueries(AMNA_KEYS.status());
      },
      onAutomationRun: (rule) => {
        addActivity({
          id: `automation-${Date.now()}`,
          timestamp: new Date(),
          source: 'amna',
          type: 'automation',
          title: `Automation Rule Executed: ${rule.name}`,
          description: `Rule "${rule.name}" was triggered and executed successfully`,
          category: rule.category,
        });
        queryClient.invalidateQueries(AMNA_KEYS.automationRules());
      },
      onError: (error) => {
        console.error('AMNA WebSocket error:', error);
        addAlert({
          id: `ws-error-${Date.now()}`,
          timestamp: new Date(),
          severity: 'error',
          source: 'amna',
          title: 'AMNA Connection Error',
          message: 'Lost connection to AMNA intelligence service',
          resolved: false,
        });
      },
    });

    return unsubscribe;
  }, [queryClient, addActivity, addAlert, setAmnaStatus, setAmnaMetrics]);
}

// Combined Hook for AMNA Dashboard
export function useAMNADashboard(timeRange: string = '7d') {
  const status = useAMNAStatus();
  const metrics = useAMNAMetrics(timeRange);
  const performance = useAMNAPerformance(timeRange);
  const insights = useAMNAInsights({ limit: 20 });
  const recommendations = useAMNARecommendations();
  const automationRules = useAMNAAutomationRules();
  const contextSources = useAMNAContextSources();

  // Subscribe to WebSocket updates
  useAMNAWebSocket();

  return {
    status,
    metrics,
    performance,
    insights,
    recommendations,
    automationRules,
    contextSources,
    isLoading: status.isLoading || metrics.isLoading,
    error: status.error || metrics.error,
  };
}