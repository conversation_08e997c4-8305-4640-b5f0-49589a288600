'use client';

import React, { forwardRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface AccessibleCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description?: string;
  children: React.ReactNode;
  actionable?: boolean;
  ariaLabel?: string;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

const AccessibleCard = forwardRef<HTMLDivElement, AccessibleCardProps>(
  ({ 
    title, 
    description, 
    children, 
    className, 
    actionable = false,
    ariaLabel,
    onKeyDown,
    ...props 
  }, ref) => {
    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (actionable && (event.key === 'Enter' || event.key === ' ')) {
        event.preventDefault();
        props.onClick?.(event as any);
      }
      onKeyDown?.(event);
    };

    return (
      <Card
        ref={ref}
        className={cn(
          className,
          actionable && 'cursor-pointer hover:shadow-lg transition-shadow focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary'
        )}
        tabIndex={actionable ? 0 : undefined}
        role={actionable ? 'button' : undefined}
        aria-label={ariaLabel || title}
        onKeyDown={handleKeyDown}
        {...props}
      >
        <CardHeader>
          <CardTitle id={`card-title-${title.replace(/\s+/g, '-').toLowerCase()}`}>
            {title}
          </CardTitle>
          {description && (
            <CardDescription 
              id={`card-desc-${title.replace(/\s+/g, '-').toLowerCase()}`}
            >
              {description}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent
          aria-labelledby={`card-title-${title.replace(/\s+/g, '-').toLowerCase()}`}
          aria-describedby={description ? `card-desc-${title.replace(/\s+/g, '-').toLowerCase()}` : undefined}
        >
          {children}
        </CardContent>
      </Card>
    );
  }
);

AccessibleCard.displayName = 'AccessibleCard';

export default AccessibleCard;