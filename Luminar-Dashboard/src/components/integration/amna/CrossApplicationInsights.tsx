'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Mail,
  Search,
  GraduationCap,
  Users,
  Trophy,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart2,
  Loader2
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAMNAInsights } from '@/hooks/useAMNA';
import { format } from 'date-fns';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface InsightItem {
  id: string;
  type: 'recommendation' | 'alert' | 'achievement' | 'trend';
  source: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  timestamp: string;
  data?: any;
}

const mockInsights: InsightItem[] = [
  {
    id: '1',
    type: 'recommendation',
    source: 'E-Connect + Training',
    title: 'Training Opportunity Identified',
    description: 'Based on recent email communications, 3 team members would benefit from advanced Excel training',
    impact: 'high',
    timestamp: '2 hours ago',
    data: { users: ['John Doe', 'Jane Smith', 'Bob Wilson'], skill: 'Excel', level: 'Advanced' }
  },
  {
    id: '2',
    type: 'alert',
    source: 'Vendors + Lighthouse',
    title: 'Vendor Performance Concern',
    description: 'Research indicates potential issues with vendor delivery times based on industry trends',
    impact: 'medium',
    timestamp: '4 hours ago',
    data: { vendor: 'TechSupplies Inc', metric: 'delivery_time', variance: '+23%' }
  },
  {
    id: '3',
    type: 'achievement',
    source: 'All Applications',
    title: 'Productivity Milestone Reached',
    description: 'Team efficiency increased by 32% this month across all integrated applications',
    impact: 'high',
    timestamp: '1 day ago',
    data: { improvement: 32, applications: 5, timeframe: 'month' }
  },
  {
    id: '4',
    type: 'trend',
    source: 'Lighthouse + E-Connect',
    title: 'Emerging Market Opportunity',
    description: 'Research correlates with email patterns suggesting new market segment interest',
    impact: 'medium',
    timestamp: '3 days ago',
    data: { segment: 'Healthcare Tech', growth: '+15%', confidence: 87 }
  }
];

const sourceIcons = {
  'E-Connect': Mail,
  'Lighthouse': Search,
  'Training': GraduationCap,
  'Vendors': Users,
  'Wins': Trophy,
};

const typeStyles = {
  recommendation: { color: 'text-blue-500', bg: 'bg-blue-100 dark:bg-blue-900/30', icon: TrendingUp },
  alert: { color: 'text-yellow-500', bg: 'bg-yellow-100 dark:bg-yellow-900/30', icon: AlertCircle },
  achievement: { color: 'text-green-500', bg: 'bg-green-100 dark:bg-green-900/30', icon: CheckCircle },
  trend: { color: 'text-purple-500', bg: 'bg-purple-100 dark:bg-purple-900/30', icon: BarChart2 },
};

export default function CrossApplicationInsights() {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedInsight, setSelectedInsight] = useState<InsightItem | null>(null);
  const isMobile = useMediaQuery('(max-width: 640px)');
  
  const { data: apiInsights, isLoading, error } = useAMNAInsights({ limit: 20 });
  
  // Transform API data to match component structure
  const insights: InsightItem[] = apiInsights?.map(insight => ({
    id: insight.id,
    type: insight.type,
    source: insight.source,
    title: insight.title,
    description: insight.description,
    impact: insight.priority as 'high' | 'medium' | 'low',
    timestamp: typeof insight.timestamp === 'string' 
      ? insight.timestamp 
      : format(new Date(insight.timestamp), 'MMM d, h:mm a'),
    data: insight.metadata
  })) || mockInsights;

  const filteredInsights = activeTab === 'all' 
    ? insights 
    : insights.filter(i => i.type === activeTab);

  const renderInsightDetails = (insight: InsightItem) => {
    switch (insight.type) {
      case 'recommendation':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Badge variant="outline">Training Recommendation</Badge>
              <Badge variant="secondary">{insight.data?.skill}</Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground mb-2">Affected Users:</p>
              <div className="flex flex-wrap gap-2">
                {insight.data?.users?.map((user: string) => (
                  <Badge key={user} variant="outline">{user}</Badge>
                ))}
              </div>
            </div>
            <Progress value={75} className="h-2" />
            <p className="text-xs text-muted-foreground">75% confidence based on communication patterns</p>
          </div>
        );

      case 'alert':
        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <span className="font-medium">{insight.data?.vendor}</span>
              <Badge variant="destructive">{insight.data?.variance}</Badge>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Metric</p>
                <p className="font-medium capitalize">{insight.data?.metric?.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Industry Average</p>
                <p className="font-medium">3-5 days</p>
              </div>
            </div>
          </div>
        );

      case 'achievement':
        return (
          <div className="space-y-3">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                +{insight.data?.improvement}%
              </p>
              <p className="text-sm text-muted-foreground">Productivity Increase</p>
            </div>
            <div className="flex justify-around text-center">
              <div>
                <p className="text-lg font-medium">{insight.data?.applications}</p>
                <p className="text-xs text-muted-foreground">Applications</p>
              </div>
              <div>
                <p className="text-lg font-medium">1</p>
                <p className="text-xs text-muted-foreground">{insight.data?.timeframe}</p>
              </div>
            </div>
          </div>
        );

      case 'trend':
        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Badge variant="outline">{insight.data?.segment}</Badge>
              <span className="text-sm font-medium text-green-500">{insight.data?.growth}</span>
            </div>
            <Progress value={insight.data?.confidence} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {insight.data?.confidence}% confidence score
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className={isMobile ? 'text-lg' : ''}>Cross-Application Insights</CardTitle>
            <CardDescription className={isMobile ? 'text-sm' : ''}>
              AI-powered insights from analyzing data across all integrated applications
            </CardDescription>
          </div>
          {isLoading && (
            <Loader2 className="w-5 h-5 animate-spin text-muted-foreground" />
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={`${
            isMobile 
              ? 'flex w-full overflow-x-auto' 
              : 'grid w-full grid-cols-5'
          }`}>
            <TabsTrigger value="all" className={isMobile ? 'min-w-fit' : ''}>All</TabsTrigger>
            <TabsTrigger value="recommendation" className={isMobile ? 'min-w-fit' : ''}>
              {isMobile ? 'Recommend' : 'Recommendations'}
            </TabsTrigger>
            <TabsTrigger value="alert" className={isMobile ? 'min-w-fit' : ''}>Alerts</TabsTrigger>
            <TabsTrigger value="achievement" className={isMobile ? 'min-w-fit' : ''}>
              {isMobile ? 'Achieve' : 'Achievements'}
            </TabsTrigger>
            <TabsTrigger value="trend" className={isMobile ? 'min-w-fit' : ''}>Trends</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            <div className="space-y-4">
              {isLoading ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-32 w-full" />
                ))
              ) : error ? (
                <div className="text-center py-8 text-muted-foreground">
                  <AlertCircle className="w-8 h-8 mx-auto mb-2 text-destructive" />
                  <p>Failed to load insights. Using cached data.</p>
                </div>
              ) : filteredInsights.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No insights available for this filter.</p>
                </div>
              ) : (
                filteredInsights.map((insight, index) => {
                const TypeIcon = typeStyles[insight.type].icon;
                const sources = insight.source.split(' + ');

                return (
                  <motion.div
                    key={insight.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      selectedInsight?.id === insight.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => setSelectedInsight(insight)}
                  >
                    <div className={`flex ${isMobile ? 'flex-col' : 'items-start'} gap-4`}>
                      <div className={`flex items-start gap-3 ${isMobile ? 'w-full' : ''}`}>
                        <div className={`p-2 rounded-lg ${typeStyles[insight.type].bg} ${isMobile ? 'shrink-0' : ''}`}>
                          <TypeIcon className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} ${typeStyles[insight.type].color}`} />
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-start justify-between'}`}>
                            <div className="flex-1">
                              <h4 className={`font-medium ${isMobile ? 'text-sm' : ''}`}>{insight.title}</h4>
                              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mt-1`}>
                                {insight.description}
                              </p>
                            </div>
                            <Badge 
                              variant={
                                insight.impact === 'high' ? 'destructive' : 
                                insight.impact === 'medium' ? 'default' : 
                                'secondary'
                              }
                              className={isMobile ? 'self-start' : ''}
                            >
                              {insight.impact}
                            </Badge>
                          </div>

                          <div className={`flex ${isMobile ? 'flex-col' : 'items-center'} gap-4 text-xs text-muted-foreground`}>
                            <div className="flex items-center gap-1">
                              {sources.map(src => {
                                const Icon = sourceIcons[src.trim() as keyof typeof sourceIcons] || Users;
                                return <Icon key={src} className="w-3 h-3" />;
                              })}
                              <span className="ml-1">{insight.source}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              <span>{insight.timestamp}</span>
                            </div>
                          </div>

                          {selectedInsight?.id === insight.id && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              transition={{ duration: 0.3 }}
                              className="pt-4 border-t"
                            >
                              {renderInsightDetails(insight)}
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
                })
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}