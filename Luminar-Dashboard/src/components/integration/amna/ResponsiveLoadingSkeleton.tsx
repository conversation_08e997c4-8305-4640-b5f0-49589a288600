'use client';

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface ResponsiveLoadingSkeletonProps {
  type?: 'card' | 'chart' | 'list' | 'table';
  className?: string;
}

export function ResponsiveLoadingSkeleton({ 
  type = 'card', 
  className = '' 
}: ResponsiveLoadingSkeletonProps) {
  const isMobile = useMediaQuery('(max-width: 640px)');

  switch (type) {
    case 'card':
      return (
        <div className={`space-y-3 ${className}`}>
          <Skeleton className={`${isMobile ? 'h-20' : 'h-24'} w-full`} />
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      );

    case 'chart':
      return (
        <div className={`space-y-3 ${className}`}>
          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-24" />
          </div>
          <Skeleton className={`${isMobile ? 'h-48' : 'h-64'} w-full`} />
        </div>
      );

    case 'list':
      return (
        <div className={`space-y-2 ${className}`}>
          {Array.from({ length: isMobile ? 3 : 5 }).map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      );

    case 'table':
      return (
        <div className={`space-y-3 ${className}`}>
          <Skeleton className="h-10 w-full" />
          {Array.from({ length: isMobile ? 3 : 5 }).map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      );

    default:
      return <Skeleton className={`h-96 w-full ${className}`} />;
  }
}

export function AMNALoadingState() {
  const isMobile = useMediaQuery('(max-width: 640px)');

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className={`flex ${isMobile ? 'flex-col' : 'items-center'} gap-4 mb-4`}>
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              {!isMobile && <Skeleton className="h-4 w-64" />}
            </div>
          </div>
          <div className={`flex gap-2 ${isMobile ? 'w-full' : 'ml-auto'}`}>
            <Skeleton className={`h-10 ${isMobile ? 'flex-1' : 'w-32'}`} />
            <Skeleton className={`h-10 ${isMobile ? 'flex-1' : 'w-24'}`} />
          </div>
        </div>
        <Skeleton className={`${isMobile ? 'h-40' : 'h-20'} w-full`} />
      </div>

      {/* Main Grid Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <ResponsiveLoadingSkeleton type="chart" />
          <ResponsiveLoadingSkeleton type="card" />
          <ResponsiveLoadingSkeleton type="chart" />
        </div>
        <div className="space-y-6">
          <ResponsiveLoadingSkeleton type="list" />
          <ResponsiveLoadingSkeleton type="card" />
        </div>
      </div>
    </div>
  );
}