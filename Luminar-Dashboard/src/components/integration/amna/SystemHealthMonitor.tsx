'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAMNAStatus, useAMNAMetrics } from '@/hooks/useAMNA';
import { cn } from '@/lib/utils';

interface HealthMetric {
  name: string;
  value: number;
  max: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  icon: React.ElementType;
}

const getHealthStatus = (value: number, max: number, inverse = false): 'healthy' | 'warning' | 'critical' => {
  const percentage = (value / max) * 100;
  
  if (inverse) {
    if (percentage > 80) return 'critical';
    if (percentage > 60) return 'warning';
    return 'healthy';
  } else {
    if (percentage < 30) return 'critical';
    if (percentage < 60) return 'warning';
    return 'healthy';
  }
};

const statusColors = {
  healthy: 'text-green-500 bg-green-100 dark:bg-green-900/30',
  warning: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/30',
  critical: 'text-red-500 bg-red-100 dark:bg-red-900/30',
};

const trendIcons = {
  up: TrendingUp,
  down: TrendingDown,
  stable: Minus,
};

export default function SystemHealthMonitor() {
  const { data: status, isLoading: statusLoading } = useAMNAStatus();
  const { data: metrics, isLoading: metricsLoading } = useAMNAMetrics();
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  // Calculate health metrics
  const healthMetrics: HealthMetric[] = [
    {
      name: 'Response Time',
      value: metrics?.responseTime || 0,
      max: 1000, // 1 second max
      unit: 'ms',
      status: getHealthStatus(metrics?.responseTime || 0, 1000, true),
      trend: 'down',
      icon: Zap,
    },
    {
      name: 'Success Rate',
      value: metrics?.successRate || 0,
      max: 100,
      unit: '%',
      status: getHealthStatus(metrics?.successRate || 0, 100),
      trend: 'stable',
      icon: CheckCircle,
    },
    {
      name: 'CPU Usage',
      value: 42, // Mock value - would come from real monitoring
      max: 100,
      unit: '%',
      status: getHealthStatus(42, 100, true),
      trend: 'up',
      icon: Cpu,
    },
    {
      name: 'Memory Usage',
      value: 68, // Mock value
      max: 100,
      unit: '%',
      status: getHealthStatus(68, 100, true),
      trend: 'stable',
      icon: HardDrive,
    },
  ];

  const overallHealth = healthMetrics.every(m => m.status === 'healthy') 
    ? 'healthy' 
    : healthMetrics.some(m => m.status === 'critical')
    ? 'critical'
    : 'warning';

  const uptime = status?.uptime ? Math.floor(status.uptime / 3600) : 0; // Convert to hours

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>System Health Monitor</CardTitle>
            <CardDescription>
              Real-time AMNA system performance metrics
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant={overallHealth === 'healthy' ? 'default' : overallHealth === 'warning' ? 'secondary' : 'destructive'}
              className="capitalize"
            >
              {overallHealth}
            </Badge>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              <RefreshCw className={cn("w-4 h-4", autoRefresh && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* System Status */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Activity className={cn(
                "w-5 h-5",
                status?.status === 'active' ? "text-green-500" : "text-gray-400"
              )} />
              <span className="font-medium">System Status</span>
            </div>
            <div className="flex items-center gap-4 text-sm">
              <span className="text-muted-foreground">
                Uptime: <span className="font-medium">{uptime}h</span>
              </span>
              <span className="text-muted-foreground">
                Version: <span className="font-medium">{status?.version || 'N/A'}</span>
              </span>
            </div>
          </div>
          
          {overallHealth !== 'healthy' && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                System performance is degraded. Check individual metrics below.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Health Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          {healthMetrics.map((metric, index) => {
            const Icon = metric.icon;
            const TrendIcon = trendIcons[metric.trend];
            const percentage = (metric.value / metric.max) * 100;
            
            return (
              <motion.div
                key={metric.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="p-4 rounded-lg border"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className={cn("p-2 rounded", statusColors[metric.status])}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">{metric.name}</p>
                      <p className="text-2xl font-bold">
                        {metric.value}
                        <span className="text-sm font-normal text-muted-foreground ml-1">
                          {metric.unit}
                        </span>
                      </p>
                    </div>
                  </div>
                  <TrendIcon className={cn(
                    "w-4 h-4",
                    metric.trend === 'up' && metric.name.includes('Rate') ? "text-green-500" :
                    metric.trend === 'up' ? "text-red-500" :
                    metric.trend === 'down' && metric.name.includes('Time') ? "text-green-500" :
                    metric.trend === 'down' ? "text-red-500" :
                    "text-gray-400"
                  )} />
                </div>
                
                <Progress 
                  value={percentage} 
                  className="h-2"
                  indicatorClassName={cn(
                    metric.status === 'healthy' && "bg-green-500",
                    metric.status === 'warning' && "bg-yellow-500",
                    metric.status === 'critical' && "bg-red-500"
                  )}
                />
                
                <div className="flex items-center justify-between mt-2">
                  <Badge variant="outline" className="text-xs">
                    {metric.status}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    of {metric.max}{metric.unit}
                  </span>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-muted-foreground mt-0.5" />
            <div className="text-sm text-muted-foreground">
              <p className="font-medium mb-1">Health Monitoring</p>
              <p>System metrics are monitored in real-time. Thresholds are automatically adjusted based on historical performance and current load.</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}