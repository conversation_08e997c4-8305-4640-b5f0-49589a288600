'use client';

import React, { useState, useMemo, memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar,
  AreaChart,
  Area,
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Cpu,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Info,
  Loader2
} from 'lucide-react';
import { useAMNAPerformance, useAMNAMetrics } from '@/hooks/useAMNA';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { ResponsiveLoadingSkeleton } from './ResponsiveLoadingSkeleton';

// Response Time Data
const responseTimeData = [
  { time: '00:00', value: 145 },
  { time: '04:00', value: 132 },
  { time: '08:00', value: 189 },
  { time: '12:00', value: 234 },
  { time: '16:00', value: 198 },
  { time: '20:00', value: 167 },
  { time: '24:00', value: 143 }
];

// Query Volume Data
const queryVolumeData = [
  { day: 'Mon', queries: 1245, successful: 1198, failed: 47 },
  { day: 'Tue', queries: 1389, successful: 1342, failed: 47 },
  { day: 'Wed', queries: 1456, successful: 1398, failed: 58 },
  { day: 'Thu', queries: 1523, successful: 1476, failed: 47 },
  { day: 'Fri', queries: 1678, successful: 1634, failed: 44 },
  { day: 'Sat', queries: 892, successful: 867, failed: 25 },
  { day: 'Sun', queries: 945, successful: 923, failed: 22 }
];

// Context Quality Data
const contextQualityData = [
  { source: 'E-Connect', quality: 94, relevance: 89 },
  { source: 'Lighthouse', quality: 89, relevance: 92 },
  { source: 'Training', quality: 91, relevance: 87 },
  { source: 'Vendors', quality: 87, relevance: 85 },
  { source: 'Wins', quality: 95, relevance: 90 }
];

// Intelligence Distribution
const fallbackIntelligenceDistribution = [
  { name: 'Basic Queries', value: 35, color: '#8884d8' },
  { name: 'Complex Analysis', value: 30, color: '#82ca9d' },
  { name: 'Predictions', value: 20, color: '#ffc658' },
  { name: 'Recommendations', value: 15, color: '#ff8042' }
];

interface MetricCard {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ElementType;
}

const metricCards: MetricCard[] = [
  {
    title: 'Avg Response Time',
    value: '178ms',
    change: '-12%',
    trend: 'down',
    icon: Clock
  },
  {
    title: 'Success Rate',
    value: '96.4%',
    change: '****%',
    trend: 'up',
    icon: CheckCircle
  },
  {
    title: 'Query Volume',
    value: '9,128',
    change: '+18%',
    trend: 'up',
    icon: MessageSquare
  },
  {
    title: 'Context Quality',
    value: '91.2%',
    change: '+0.8%',
    trend: 'up',
    icon: Target
  }
];

const AMNAPerformanceMetrics = memo(function AMNAPerformanceMetrics() {
  const [timeRange, setTimeRange] = useState('7d');
  const [activeTab, setActiveTab] = useState('overview');
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  
  const { data: performanceData, isLoading: performanceLoading } = useAMNAPerformance(timeRange);
  const { data: metricsData, isLoading: metricsLoading } = useAMNAMetrics(timeRange);
  
  const isLoading = performanceLoading || metricsLoading;
  
  // Use API data if available, otherwise use fallback - memoized for performance
  const responseTime = useMemo(
    () => performanceData?.responseTime || responseTimeData,
    [performanceData?.responseTime]
  );
  
  const queryVolume = useMemo(
    () => performanceData?.queryVolume || queryVolumeData,
    [performanceData?.queryVolume]
  );
  
  const contextQuality = useMemo(
    () => performanceData?.contextQuality || contextQualityData,
    [performanceData?.contextQuality]
  );
  
  const intelligenceDistribution = useMemo(
    () => performanceData?.intelligenceDistribution || fallbackIntelligenceDistribution,
    [performanceData?.intelligenceDistribution]
  );

  return (
    <Card>
      <CardHeader>
        <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'}`}>
          <div>
            <CardTitle className={isMobile ? 'text-lg' : ''}>Performance Metrics</CardTitle>
            <CardDescription className={isMobile ? 'text-sm' : ''}>
              Real-time AMNA intelligence performance monitoring
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className={isMobile ? 'w-full' : 'w-32'}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {/* Metric Cards */}
        {isLoading ? (
          <ResponsiveLoadingSkeleton type="card" className="mb-6" />
        ) : (
          <div className={`grid ${
            isMobile ? 'grid-cols-2' : isTablet ? 'grid-cols-2' : 'grid-cols-4'
          } gap-4 mb-6`}>
            {metricCards.map((metric) => {
            const Icon = metric.icon;
            return (
              <div key={metric.title} className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Icon className="w-4 h-4 text-muted-foreground" />
                  <Badge 
                    variant={metric.trend === 'up' ? 'default' : metric.trend === 'down' ? 'secondary' : 'outline'}
                    className="text-xs"
                  >
                    {metric.change}
                  </Badge>
                </div>
                <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{metric.value}</p>
                <p className="text-xs text-muted-foreground">{metric.title}</p>
              </div>
            );
            })}
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={`${
            isMobile 
              ? 'flex w-full overflow-x-auto' 
              : 'grid w-full grid-cols-4'
          }`}>
            <TabsTrigger value="overview" className={isMobile ? 'min-w-fit' : ''}>Overview</TabsTrigger>
            <TabsTrigger value="response" className={isMobile ? 'min-w-fit' : ''}>Response Time</TabsTrigger>
            <TabsTrigger value="volume" className={isMobile ? 'min-w-fit' : ''}>Query Volume</TabsTrigger>
            <TabsTrigger value="quality" className={isMobile ? 'min-w-fit' : ''}>Context Quality</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-2'} gap-6`}>
              {/* Query Volume Trend */}
              <div>
                <h4 className="text-sm font-medium mb-4">Query Volume Trend</h4>
                <ResponsiveContainer width="100%" height={isMobile ? 180 : 200}>
                  <AreaChart data={queryVolume}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis dataKey="day" className="text-xs" />
                    <YAxis className="text-xs" />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="queries" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>

              {/* Intelligence Distribution */}
              <div>
                <h4 className="text-sm font-medium mb-4">Query Type Distribution</h4>
                <ResponsiveContainer width="100%" height={isMobile ? 180 : 200}>
                  <PieChart>
                    <Pie
                      data={intelligenceDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={isMobile ? false : ({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={isMobile ? 60 : 80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {(intelligenceDistribution || fallbackIntelligenceDistribution).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                    {isMobile && <Legend verticalAlign="bottom" height={36} />}
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="response" className="mt-6">
            <div className="space-y-4">
              <div className={`${
                isMobile ? 'grid grid-cols-1 gap-2' : 'flex items-center gap-4'
              }`}>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <span className="text-sm">Good ({"<"}200ms)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <span className="text-sm">Fair (200-500ms)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <span className="text-sm">Poor ({">"}500ms)</span>
                </div>
              </div>

              <ResponsiveContainer width="100%" height={isMobile ? 200 : 250}>
                <LineChart data={responseTime}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis dataKey="time" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    dot={isMobile ? false : { fill: '#8884d8' }}
                  />
                  {/* Threshold lines */}
                  <Line 
                    type="monotone" 
                    dataKey={() => 200} 
                    stroke="#eab308" 
                    strokeDasharray="5 5"
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey={() => 500} 
                    stroke="#ef4444" 
                    strokeDasharray="5 5"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="volume" className="mt-6">
            <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
              <BarChart data={queryVolume}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis dataKey="day" className="text-xs" />
                <YAxis className="text-xs" />
                <Tooltip />
                <Legend />
                <Bar dataKey="successful" stackId="a" fill="#82ca9d" name="Successful" />
                <Bar dataKey="failed" stackId="a" fill="#ff8042" name="Failed" />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="quality" className="mt-6">
            <div className="space-y-4">
              {contextQuality.map((source) => (
                <div key={source.source} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{source.source}</h4>
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">
                        Quality: {source.quality}%
                      </Badge>
                      <Badge variant="outline">
                        Relevance: {source.relevance}%
                      </Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Quality</span>
                        <span>{source.quality}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all"
                          style={{ width: `${source.quality}%` }}
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Relevance</span>
                        <span>{source.relevance}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all"
                          style={{ width: `${source.relevance}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-start gap-2">
                  <Info className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="text-sm text-muted-foreground">
                    <p className="font-medium mb-1">Context Quality Score</p>
                    <p>Measures how well AMNA understands and utilizes data from each source. Higher scores indicate better integration and more accurate responses.</p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
});

export default AMNAPerformanceMetrics;