'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Lightbulb, 
  Zap, 
  Target, 
  Brain,
  ChevronRight,
  Sparkles,
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAMNARecommendations, useImplementRecommendation } from '@/hooks/useAMNA';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: 'automation' | 'optimization' | 'insight' | 'workflow';
  impact: number; // 0-100
  effort: 'low' | 'medium' | 'high';
  implemented: boolean;
  actions: string[];
}

const fallbackRecommendations: Recommendation[] = [
  {
    id: '1',
    title: 'Automate Training-Vendor Matching',
    description: 'AI can automatically match training needs with vendor capabilities based on historical data',
    category: 'automation',
    impact: 85,
    effort: 'medium',
    implemented: false,
    actions: ['Enable auto-matching', 'Configure parameters', 'Review matches']
  },
  {
    id: '2',
    title: 'Optimize Email Response Suggestions',
    description: 'Improve response time by 40% with context-aware email suggestions',
    category: 'optimization',
    impact: 72,
    effort: 'low',
    implemented: false,
    actions: ['Enable suggestions', 'Train on your data']
  },
  {
    id: '3',
    title: 'Cross-Reference Research Insights',
    description: 'Automatically correlate Lighthouse research with vendor performance data',
    category: 'insight',
    impact: 68,
    effort: 'low',
    implemented: true,
    actions: ['View insights', 'Adjust parameters']
  },
  {
    id: '4',
    title: 'Weekly Achievement Automation',
    description: 'Auto-capture and categorize achievements from all applications',
    category: 'workflow',
    impact: 60,
    effort: 'medium',
    implemented: false,
    actions: ['Configure rules', 'Set categories', 'Enable automation']
  }
];

const categoryStyles = {
  automation: { color: 'text-blue-500', bg: 'bg-blue-100 dark:bg-blue-900/30', icon: Zap },
  optimization: { color: 'text-green-500', bg: 'bg-green-100 dark:bg-green-900/30', icon: Target },
  insight: { color: 'text-purple-500', bg: 'bg-purple-100 dark:bg-purple-900/30', icon: Lightbulb },
  workflow: { color: 'text-orange-500', bg: 'bg-orange-100 dark:bg-orange-900/30', icon: Brain }
};

const effortColors = {
  low: 'text-green-600',
  medium: 'text-yellow-600',
  high: 'text-red-600'
};

export default function IntelligenceRecommendations() {
  const { data: apiRecommendations, isLoading, error } = useAMNARecommendations();
  const implementMutation = useImplementRecommendation();
  
  // Use API data if available, otherwise use fallback
  const recommendations = apiRecommendations || fallbackRecommendations;
  
  const totalImpact = recommendations.reduce((sum, rec) => sum + rec.impact, 0);
  const implementedCount = recommendations.filter(r => r.implemented).length;
  const averageImpact = recommendations.length > 0 ? totalImpact / recommendations.length : 0;
  
  const handleImplement = async (recommendationId: string) => {
    try {
      await implementMutation.mutateAsync(recommendationId);
    } catch (error) {
      console.error('Failed to implement recommendation:', error);
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>AI Recommendations</CardTitle>
            <CardDescription>
              Personalized suggestions to enhance your workflow
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-yellow-500" />
            <span className="text-sm font-medium">{implementedCount}/{recommendations.length}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        {isLoading ? (
          <div className="grid grid-cols-2 gap-4 mb-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">{Math.round(averageImpact)}%</p>
              <p className="text-xs text-muted-foreground">Avg. Impact</p>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">{implementedCount}</p>
              <p className="text-xs text-muted-foreground">Implemented</p>
            </div>
          </div>
        )}

        {/* Recommendations List */}
        <div className="space-y-3">
          {isLoading ? (
            Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))
          ) : error ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="w-8 h-8 mx-auto mb-2 text-destructive" />
              <p>Failed to load recommendations</p>
            </div>
          ) : recommendations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No recommendations available</p>
            </div>
          ) : (
            recommendations.map((rec, index) => {
            const CategoryIcon = categoryStyles[rec.category].icon;

            return (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`p-3 rounded-lg border ${
                  rec.implemented ? 'opacity-75' : ''
                }`}
              >
                <div className="space-y-2">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded ${categoryStyles[rec.category].bg}`}>
                      <CategoryIcon className={`w-4 h-4 ${categoryStyles[rec.category].color}`} />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="text-sm font-medium flex-1">{rec.title}</h4>
                        {rec.implemented && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {rec.description}
                      </p>
                      
                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-muted-foreground">Impact:</span>
                          <div className="w-16">
                            <Progress value={rec.impact} className="h-1.5" />
                          </div>
                          <span className="text-xs font-medium">{rec.impact}%</span>
                        </div>
                        <Badge variant="outline" className={`text-xs ${effortColors[rec.effort]}`}>
                          {rec.effort} effort
                        </Badge>
                      </div>

                      {!rec.implemented && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2 h-7 text-xs"
                          onClick={() => handleImplement(rec.id)}
                          disabled={implementMutation.isLoading}
                        >
                          {implementMutation.isLoading ? (
                            <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                          ) : (
                            <>
                              Implement
                              <ChevronRight className="w-3 h-3 ml-1" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
            })
          )}
        </div>

        {/* View All Button */}
        <Button variant="outline" className="w-full mt-4" size="sm">
          View All Recommendations
          <ChevronRight className="w-4 h-4 ml-1" />
        </Button>
      </CardContent>
    </Card>
  );
}