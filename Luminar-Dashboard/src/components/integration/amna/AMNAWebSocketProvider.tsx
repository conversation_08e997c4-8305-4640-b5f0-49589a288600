'use client';

import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useIntegrationStore } from '@/store/integration/integrationStore';
import { toast } from 'react-hot-toast';

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: Date;
}

interface AMNAWebSocketContextType {
  isConnected: boolean;
  lastMessage: WebSocketMessage | null;
  sendMessage: (message: any) => void;
  reconnect: () => void;
}

const AMNAWebSocketContext = createContext<AMNAWebSocketContextType | null>(null);

export const useAMNAWebSocket = () => {
  const context = useContext(AMNAWebSocketContext);
  if (!context) {
    throw new Error('useAMNAWebSocket must be used within AMNAWebSocketProvider');
  }
  return context;
};

interface AMNAWebSocketProviderProps {
  children: React.ReactNode;
  url?: string;
}

export function AMNAWebSocketProvider({ children, url }: AMNAWebSocketProviderProps) {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  
  const { 
    addActivity, 
    addAlert,
    setWebSocketStatus,
    processWebSocketMessage 
  } = useIntegrationStore();

  const wsUrl = url || process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000';

  const connect = () => {
    try {
      const ws = new WebSocket(`${wsUrl}/amna`);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('AMNA WebSocket connected');
        setIsConnected(true);
        setWebSocketStatus(true);
        
        // Send initial authentication/subscription message if needed
        ws.send(JSON.stringify({
          type: 'subscribe',
          channels: ['insights', 'metrics', 'automation', 'alerts']
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const message: WebSocketMessage = {
            type: data.type,
            payload: data.payload,
            timestamp: new Date()
          };
          
          setLastMessage(message);
          
          // Process different message types
          switch (data.type) {
            case 'activity':
              addActivity({
                id: `activity-${Date.now()}`,
                timestamp: new Date(),
                source: 'amna',
                type: data.payload.type || 'general',
                title: data.payload.title,
                description: data.payload.description,
                category: data.payload.category,
                metadata: data.payload.metadata
              });
              break;
              
            case 'alert':
              addAlert({
                id: `alert-${Date.now()}`,
                timestamp: new Date(),
                severity: data.payload.severity || 'info',
                source: 'amna',
                title: data.payload.title,
                message: data.payload.message,
                resolved: false
              });
              break;
              
            case 'metric':
              // Process metric updates
              processWebSocketMessage({
                type: 'metrics.updated',
                payload: {
                  type: 'intelligence',
                  data: data.payload
                }
              });
              break;
              
            case 'insight':
              // Add as activity for insights
              addActivity({
                id: `insight-${Date.now()}`,
                timestamp: new Date(),
                source: 'amna',
                type: 'insight',
                title: data.payload.title,
                description: data.payload.description,
                category: data.payload.category,
                metadata: {
                  priority: data.payload.priority,
                  source: data.payload.source
                }
              });
              break;
              
            default:
              console.log('Unknown WebSocket message type:', data.type);
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error);
        }
      };

      ws.onerror = (error) => {
        console.error('AMNA WebSocket error:', error);
        toast.error('Connection error with AMNA service');
      };

      ws.onclose = () => {
        console.log('AMNA WebSocket disconnected');
        setIsConnected(false);
        setWebSocketStatus(false);
        
        // Attempt to reconnect after 5 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          console.log('Attempting to reconnect...');
          connect();
        }, 5000);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setIsConnected(false);
      setWebSocketStatus(false);
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  const sendMessage = (message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
      toast.error('Cannot send message: Not connected to AMNA');
    }
  };

  const reconnect = () => {
    disconnect();
    connect();
  };

  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, []);

  const contextValue: AMNAWebSocketContextType = {
    isConnected,
    lastMessage,
    sendMessage,
    reconnect
  };

  return (
    <AMNAWebSocketContext.Provider value={contextValue}>
      {children}
    </AMNAWebSocketContext.Provider>
  );
}