'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar,
  PieChart, 
  Pie, 
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  TrendingUp, 
  Brain, 
  Target, 
  Clock,
  MessageSquare,
  Layers,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from 'lucide-react';
import { useAMNAQueryHistory } from '@/hooks/useAMNA';

// Mock data for query patterns
const queryPatterns = [
  { hour: '00', queries: 45, avgTime: 156 },
  { hour: '04', queries: 32, avgTime: 143 },
  { hour: '08', queries: 189, avgTime: 178 },
  { hour: '12', queries: 234, avgTime: 192 },
  { hour: '16', queries: 198, avgTime: 165 },
  { hour: '20', queries: 167, avgTime: 171 },
];

const queryTypes = [
  { name: 'Information Retrieval', value: 35, color: '#8884d8' },
  { name: 'Data Analysis', value: 25, color: '#82ca9d' },
  { name: 'Predictive', value: 20, color: '#ffc658' },
  { name: 'Recommendation', value: 15, color: '#ff8042' },
  { name: 'Automation', value: 5, color: '#a4de6c' },
];

const applicationUsage = [
  { app: 'E-Connect', queries: 450, successRate: 96 },
  { app: 'Lighthouse', queries: 320, successRate: 94 },
  { app: 'Training', queries: 280, successRate: 97 },
  { app: 'Vendors', queries: 150, successRate: 92 },
  { app: 'Wins', queries: 100, successRate: 98 },
];

const complexityAnalysis = [
  { complexity: 'Simple', avgTime: 50, count: 450, satisfaction: 98 },
  { complexity: 'Medium', avgTime: 150, count: 320, satisfaction: 94 },
  { complexity: 'Complex', avgTime: 300, count: 180, satisfaction: 89 },
  { complexity: 'Advanced', avgTime: 500, count: 50, satisfaction: 85 },
];

const performanceRadar = [
  { metric: 'Speed', A: 85, B: 75, fullMark: 100 },
  { metric: 'Accuracy', A: 94, B: 88, fullMark: 100 },
  { metric: 'Relevance', A: 92, B: 85, fullMark: 100 },
  { metric: 'Coverage', A: 88, B: 80, fullMark: 100 },
  { metric: 'Context', A: 90, B: 82, fullMark: 100 },
];

export default function QueryAnalytics() {
  const [timeRange, setTimeRange] = useState('7d');
  const [activeTab, setActiveTab] = useState('patterns');
  const { data: queryHistory, isLoading } = useAMNAQueryHistory(50);

  const totalQueries = queryTypes.reduce((sum, type) => sum + type.value, 0);
  const avgResponseTime = queryPatterns.reduce((sum, p) => sum + p.avgTime, 0) / queryPatterns.length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Query Analytics</CardTitle>
            <CardDescription>
              Deep insights into AMNA query patterns and performance
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <MessageSquare className="w-5 h-5 mx-auto mb-1 text-primary" />
            <p className="text-2xl font-bold">{totalQueries * 10}</p>
            <p className="text-xs text-muted-foreground">Total Queries</p>
          </div>
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <Clock className="w-5 h-5 mx-auto mb-1 text-primary" />
            <p className="text-2xl font-bold">{Math.round(avgResponseTime)}ms</p>
            <p className="text-xs text-muted-foreground">Avg Response</p>
          </div>
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <Target className="w-5 h-5 mx-auto mb-1 text-primary" />
            <p className="text-2xl font-bold">94.2%</p>
            <p className="text-xs text-muted-foreground">Success Rate</p>
          </div>
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <Brain className="w-5 h-5 mx-auto mb-1 text-primary" />
            <p className="text-2xl font-bold">4.8/5</p>
            <p className="text-xs text-muted-foreground">User Satisfaction</p>
          </div>
        </div>

        {/* Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="patterns">Query Patterns</TabsTrigger>
            <TabsTrigger value="types">Query Types</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="applications">By Application</TabsTrigger>
          </TabsList>

          <TabsContent value="patterns" className="mt-6">
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">Query Volume & Response Time by Hour</h4>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={queryPatterns}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis dataKey="hour" className="text-xs" />
                    <YAxis yAxisId="left" className="text-xs" />
                    <YAxis yAxisId="right" orientation="right" className="text-xs" />
                    <Tooltip />
                    <Legend />
                    <Line 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="queries" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      name="Query Volume"
                    />
                    <Line 
                      yAxisId="right"
                      type="monotone" 
                      dataKey="avgTime" 
                      stroke="#82ca9d" 
                      strokeWidth={2}
                      name="Avg Response (ms)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="types" className="mt-6">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium mb-4">Query Type Distribution</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={queryTypes}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {queryTypes.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-4">Complexity Analysis</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={complexityAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis dataKey="complexity" className="text-xs" />
                    <YAxis className="text-xs" />
                    <Tooltip />
                    <Bar dataKey="avgTime" fill="#8884d8" name="Avg Time (ms)" />
                    <Bar dataKey="satisfaction" fill="#82ca9d" name="Satisfaction %" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="mt-6">
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">Performance Comparison</h4>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={performanceRadar}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="metric" className="text-xs" />
                    <PolarRadiusAxis angle={90} domain={[0, 100]} />
                    <Radar name="Current" dataKey="A" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    <Radar name="Previous" dataKey="B" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </div>

              <div className="grid grid-cols-3 gap-4">
                {['Speed', 'Accuracy', 'Relevance'].map((metric) => {
                  const data = performanceRadar.find(d => d.metric === metric);
                  const improvement = data ? ((data.A - data.B) / data.B * 100).toFixed(1) : 0;
                  
                  return (
                    <div key={metric} className="p-4 rounded-lg border">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{metric}</span>
                        <Badge variant={Number(improvement) > 0 ? 'default' : 'secondary'}>
                          {Number(improvement) > 0 ? '+' : ''}{improvement}%
                        </Badge>
                      </div>
                      <div className="text-2xl font-bold">{data?.A}%</div>
                      <p className="text-xs text-muted-foreground">vs {data?.B}% previous</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="applications" className="mt-6">
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">Query Distribution by Application</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={applicationUsage} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis type="number" className="text-xs" />
                    <YAxis type="category" dataKey="app" className="text-xs" />
                    <Tooltip />
                    <Bar dataKey="queries" fill="#8884d8" name="Query Count" />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="space-y-3">
                {applicationUsage.map((app) => (
                  <div key={app.app} className="flex items-center justify-between p-3 rounded-lg border">
                    <div>
                      <p className="font-medium">{app.app}</p>
                      <p className="text-sm text-muted-foreground">{app.queries} queries</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="mb-1">
                        {app.successRate}% success
                      </Badge>
                      <p className="text-xs text-muted-foreground">
                        Avg time: {Math.floor(Math.random() * 100 + 100)}ms
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}