'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import Link from 'next/link';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorCount: number;
}

export default class AMNAErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      errorCount: 0,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('AMNA Error Boundary caught an error:', error, errorInfo);
    }

    // Update state with error details
    this.setState((prevState) => ({
      error,
      errorInfo,
      errorCount: prevState.errorCount + 1,
    }));

    // In production, you would send this to an error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Example: sendToErrorTracker(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
    });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      // Default error UI
      return (
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <CardTitle>AMNA Integration Error</CardTitle>
                  <CardDescription>
                    Something went wrong with the AMNA Intelligence Center
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription className="mt-2">
                  <p className="font-mono text-sm">
                    {this.state.error?.message || 'An unexpected error occurred'}
                  </p>
                  {this.state.errorCount > 1 && (
                    <p className="text-sm mt-2">
                      This error has occurred {this.state.errorCount} times.
                    </p>
                  )}
                </AlertDescription>
              </Alert>

              {/* Error Stack in Development */}
              {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Component Stack:</h4>
                  <pre className="p-4 bg-muted rounded-lg overflow-x-auto text-xs">
                    {this.state.errorInfo.componentStack}
                  </pre>
                  
                  {this.state.error?.stack && (
                    <>
                      <h4 className="text-sm font-medium mt-4">Error Stack:</h4>
                      <pre className="p-4 bg-muted rounded-lg overflow-x-auto text-xs">
                        {this.state.error.stack}
                      </pre>
                    </>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex flex-wrap gap-3 pt-4">
                <Button onClick={this.handleReset} variant="default">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                
                <Link href="/integration">
                  <Button variant="outline">
                    <Home className="w-4 h-4 mr-2" />
                    Go to Integration Home
                  </Button>
                </Link>

                {process.env.NODE_ENV === 'development' && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      console.log('Error details:', {
                        error: this.state.error,
                        errorInfo: this.state.errorInfo,
                      });
                    }}
                  >
                    <Bug className="w-4 h-4 mr-2" />
                    Log Error Details
                  </Button>
                )}
              </div>

              {/* Help Text */}
              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  If this error persists, please try:
                </p>
                <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                  <li>Refreshing the page</li>
                  <li>Checking your internet connection</li>
                  <li>Clearing your browser cache</li>
                  <li>Contacting support if the issue continues</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}