'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Settings, 
  Plus, 
  Play, 
  Pause,
  Edit,
  Trash2,
  GitBranch,
  Zap,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAMNAAutomationRules, useToggleAutomationRule, useRunAutomationRule } from '@/hooks/useAMNA';
import { format } from 'date-fns';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: string;
  actions: string[];
  enabled: boolean;
  lastRun?: string;
  runCount: number;
  category: 'email' | 'research' | 'training' | 'vendor' | 'general';
}

const automationRules: AutomationRule[] = [
  {
    id: '1',
    name: 'Auto-Tag Important Emails',
    description: 'Automatically tag and prioritize emails based on content and sender',
    trigger: 'New email received',
    actions: ['Analyze content', 'Apply tags', 'Update priority'],
    enabled: true,
    lastRun: '10 minutes ago',
    runCount: 324,
    category: 'email'
  },
  {
    id: '2',
    name: 'Research to Training Pipeline',
    description: 'Convert research insights into training recommendations',
    trigger: 'New research completed',
    actions: ['Extract key findings', 'Identify skill gaps', 'Create training plan'],
    enabled: true,
    lastRun: '2 hours ago',
    runCount: 48,
    category: 'research'
  },
  {
    id: '3',
    name: 'Vendor Performance Alerts',
    description: 'Alert when vendor metrics fall below thresholds',
    trigger: 'Performance threshold breach',
    actions: ['Send notification', 'Create report', 'Schedule review'],
    enabled: false,
    lastRun: '3 days ago',
    runCount: 12,
    category: 'vendor'
  },
  {
    id: '4',
    name: 'Weekly Achievement Capture',
    description: 'Automatically capture and categorize weekly achievements',
    trigger: 'Weekly schedule (Friday 4pm)',
    actions: ['Collect activities', 'Categorize achievements', 'Generate summary'],
    enabled: true,
    lastRun: '1 week ago',
    runCount: 156,
    category: 'general'
  }
];

const categoryColors = {
  email: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300',
  research: 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300',
  training: 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300',
  vendor: 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300',
  general: 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
};

export default function AutomationRules() {
  const [expandedRule, setExpandedRule] = useState<string | null>(null);
  
  const { data: apiRules, isLoading, error } = useAMNAAutomationRules();
  const toggleRuleMutation = useToggleAutomationRule();
  const runRuleMutation = useRunAutomationRule();
  
  // Use API data if available, otherwise use fallback
  const rules = apiRules || automationRules;

  const toggleRule = async (ruleId: string, currentEnabled: boolean) => {
    try {
      await toggleRuleMutation.mutateAsync({ id: ruleId, enabled: !currentEnabled });
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  };
  
  const runRule = async (ruleId: string) => {
    try {
      await runRuleMutation.mutateAsync(ruleId);
    } catch (error) {
      console.error('Failed to run rule:', error);
    }
  };

  const activeRules = rules.filter(r => r.enabled).length;
  const totalRuns = rules.reduce((sum, rule) => sum + rule.runCount, 0);

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Automation Rules</CardTitle>
            <CardDescription>
              Intelligent automation workflows
            </CardDescription>
          </div>
          <Button size="sm" variant="outline">
            <Plus className="w-4 h-4 mr-1" />
            New Rule
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Stats */}
        {isLoading ? (
          <div className="grid grid-cols-3 gap-3 mb-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-3 mb-6">
            <div className="text-center p-2 bg-muted/50 rounded-lg">
              <p className="text-lg font-bold">{activeRules}</p>
              <p className="text-xs text-muted-foreground">Active</p>
            </div>
            <div className="text-center p-2 bg-muted/50 rounded-lg">
              <p className="text-lg font-bold">{rules.length}</p>
              <p className="text-xs text-muted-foreground">Total</p>
            </div>
            <div className="text-center p-2 bg-muted/50 rounded-lg">
              <p className="text-lg font-bold">{totalRuns}</p>
              <p className="text-xs text-muted-foreground">Runs</p>
            </div>
          </div>
        )}

        {/* Rules List */}
        <div className="space-y-3">
          {isLoading ? (
            Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))
          ) : error ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="w-8 h-8 mx-auto mb-2 text-destructive" />
              <p>Failed to load automation rules</p>
            </div>
          ) : (
            rules.map((rule, index) => (
            <motion.div
              key={rule.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className={`p-3 rounded-lg border ${
                !rule.enabled ? 'opacity-60' : ''
              }`}
            >
              <div className="space-y-2">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-medium">{rule.name}</h4>
                      <Badge variant="secondary" className={`text-xs ${categoryColors[rule.category]}`}>
                        {rule.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {rule.description}
                    </p>
                  </div>
                  <Switch
                    checked={rule.enabled}
                    onCheckedChange={() => toggleRule(rule.id, rule.enabled)}
                    disabled={toggleRuleMutation.isLoading}
                  />
                </div>

                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <GitBranch className="w-3 h-3" />
                    <span>{rule.trigger}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="w-3 h-3" />
                    <span>{rule.runCount} runs</span>
                  </div>
                  {rule.lastRun && (
                    <span>
                      {typeof rule.lastRun === 'string' 
                        ? rule.lastRun 
                        : format(new Date(rule.lastRun), 'MMM d, h:mm a')
                      }
                    </span>
                  )}
                </div>

                {expandedRule === rule.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="pt-2 border-t space-y-2"
                  >
                    <div>
                      <p className="text-xs font-medium mb-1">Actions:</p>
                      <div className="space-y-1">
                        {rule.actions.map((action, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-xs">
                            <div className="w-1 h-1 bg-primary rounded-full" />
                            <span>{action}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-2 pt-2">
                      <Button size="sm" variant="outline" className="h-7 text-xs">
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-7 text-xs"
                        onClick={() => runRule(rule.id)}
                        disabled={runRuleMutation.isLoading}
                      >
                        {runRuleMutation.isLoading ? (
                          <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                        ) : (
                          <Play className="w-3 h-3 mr-1" />
                        )}
                        Run Now
                      </Button>
                      <Button size="sm" variant="ghost" className="h-7 text-xs text-destructive">
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </motion.div>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs w-full"
                  onClick={() => setExpandedRule(expandedRule === rule.id ? null : rule.id)}
                >
                  {expandedRule === rule.id ? 'Hide Details' : 'Show Details'}
                </Button>
              </div>
            </motion.div>
            ))
          )}
        </div>

        {/* Create Rule Button */}
        <Button variant="outline" className="w-full mt-4" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          Manage All Rules
        </Button>
      </CardContent>
    </Card>
  );
}