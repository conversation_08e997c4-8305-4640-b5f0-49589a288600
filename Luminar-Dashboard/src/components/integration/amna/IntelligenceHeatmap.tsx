'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Brain, 
  TrendingUp,
  Info,
  Download,
  Filter
} from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Days of week and hours for heatmap
const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const hours = Array.from({ length: 24 }, (_, i) => i);

// Generate mock heatmap data
const generateHeatmapData = (metric: string) => {
  return days.map(day => ({
    day,
    hours: hours.map(hour => ({
      hour,
      value: Math.floor(Math.random() * 100),
      details: {
        queries: Math.floor(Math.random() * 50),
        avgTime: Math.floor(Math.random() * 200 + 100),
        accuracy: Math.floor(Math.random() * 20 + 80),
      }
    }))
  }));
};

// Color scale for heatmap
const getColor = (value: number, max: number) => {
  const intensity = value / max;
  if (intensity < 0.2) return 'bg-blue-100 dark:bg-blue-900/20';
  if (intensity < 0.4) return 'bg-blue-300 dark:bg-blue-700/40';
  if (intensity < 0.6) return 'bg-blue-500 dark:bg-blue-600/60';
  if (intensity < 0.8) return 'bg-blue-700 dark:bg-blue-500/80';
  return 'bg-blue-900 dark:bg-blue-400';
};

interface HeatmapCell {
  hour: number;
  value: number;
  details: {
    queries: number;
    avgTime: number;
    accuracy: number;
  };
}

export default function IntelligenceHeatmap() {
  const [metric, setMetric] = useState('activity');
  const [timeRange, setTimeRange] = useState('7d');
  const [hoveredCell, setHoveredCell] = useState<{ day: string; hour: number } | null>(null);
  
  const heatmapData = useMemo(() => generateHeatmapData(metric), [metric]);
  
  const maxValue = Math.max(
    ...heatmapData.flatMap(d => d.hours.map(h => h.value))
  );

  const metrics = [
    { value: 'activity', label: 'Query Activity', icon: Activity },
    { value: 'performance', label: 'Response Time', icon: TrendingUp },
    { value: 'intelligence', label: 'Intelligence Usage', icon: Brain },
  ];

  const getCellDetails = (day: string, hour: number) => {
    const dayData = heatmapData.find(d => d.day === day);
    return dayData?.hours.find(h => h.hour === hour);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Intelligence Activity Heatmap</CardTitle>
            <CardDescription>
              Visualize AMNA usage patterns across time
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={metric} onValueChange={setMetric}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {metrics.map(m => (
                  <SelectItem key={m.value} value={m.value}>
                    <div className="flex items-center gap-2">
                      <m.icon className="w-4 h-4" />
                      {m.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="heatmap" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="heatmap">Heatmap View</TabsTrigger>
            <TabsTrigger value="patterns">Usage Patterns</TabsTrigger>
          </TabsList>

          <TabsContent value="heatmap" className="mt-6">
            <div className="space-y-4">
              {/* Hour labels */}
              <div className="flex items-center gap-1">
                <div className="w-12" /> {/* Spacer for day labels */}
                {hours.map(hour => (
                  <div 
                    key={hour} 
                    className="flex-1 text-center text-xs text-muted-foreground"
                  >
                    {hour % 4 === 0 ? hour : ''}
                  </div>
                ))}
              </div>

              {/* Heatmap grid */}
              {heatmapData.map((dayData, dayIndex) => (
                <div key={dayData.day} className="flex items-center gap-1">
                  <div className="w-12 text-sm font-medium text-right pr-2">
                    {dayData.day}
                  </div>
                  {dayData.hours.map((hourData, hourIndex) => (
                    <motion.div
                      key={`${dayData.day}-${hourData.hour}`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ 
                        duration: 0.2, 
                        delay: (dayIndex * 24 + hourIndex) * 0.001 
                      }}
                      className="flex-1 aspect-square relative group"
                      onMouseEnter={() => setHoveredCell({ day: dayData.day, hour: hourData.hour })}
                      onMouseLeave={() => setHoveredCell(null)}
                    >
                      <div 
                        className={cn(
                          "w-full h-full rounded-sm transition-all cursor-pointer",
                          getColor(hourData.value, maxValue),
                          "hover:ring-2 hover:ring-primary hover:ring-offset-1"
                        )}
                      />
                      
                      {/* Tooltip */}
                      {hoveredCell?.day === dayData.day && hoveredCell?.hour === hourData.hour && (
                        <div className="absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-3 bg-popover border rounded-lg shadow-lg whitespace-nowrap">
                          <p className="font-medium text-sm">
                            {dayData.day} {hourData.hour}:00
                          </p>
                          <div className="space-y-1 mt-2 text-xs">
                            <p>Queries: <span className="font-medium">{hourData.details.queries}</span></p>
                            <p>Avg Time: <span className="font-medium">{hourData.details.avgTime}ms</span></p>
                            <p>Accuracy: <span className="font-medium">{hourData.details.accuracy}%</span></p>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              ))}

              {/* Legend */}
              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-muted-foreground">Low</span>
                  <div className="flex items-center gap-1">
                    {[0.2, 0.4, 0.6, 0.8, 1].map((intensity) => (
                      <div
                        key={intensity}
                        className={cn(
                          "w-6 h-6 rounded-sm",
                          getColor(intensity * maxValue, maxValue)
                        )}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground">High</span>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="patterns" className="mt-6">
            <div className="space-y-6">
              {/* Peak Usage Times */}
              <div>
                <h4 className="font-medium mb-3">Peak Usage Times</h4>
                <div className="space-y-2">
                  {[
                    { time: 'Weekday Mornings (8-10 AM)', value: 89, trend: '+12%' },
                    { time: 'Lunch Hours (12-1 PM)', value: 76, trend: '+5%' },
                    { time: 'Late Afternoon (3-5 PM)', value: 82, trend: '+8%' },
                    { time: 'Evening (7-9 PM)', value: 45, trend: '-3%' },
                  ].map((pattern) => (
                    <div key={pattern.time} className="flex items-center justify-between p-3 rounded-lg border">
                      <div>
                        <p className="font-medium text-sm">{pattern.time}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary transition-all"
                              style={{ width: `${pattern.value}%` }}
                            />
                          </div>
                          <span className="text-xs text-muted-foreground">{pattern.value}%</span>
                        </div>
                      </div>
                      <Badge variant={pattern.trend.startsWith('+') ? 'default' : 'secondary'}>
                        {pattern.trend}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Usage Insights */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex items-start gap-2">
                  <Info className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="text-sm text-muted-foreground">
                    <p className="font-medium mb-1">Usage Insights</p>
                    <ul className="space-y-1">
                      <li>• Highest activity during business hours (9 AM - 5 PM)</li>
                      <li>• 35% increase in queries on Mondays</li>
                      <li>• Weekend usage primarily for reporting tasks</li>
                      <li>• Response times are fastest during off-peak hours</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}