'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Network, 
  Database, 
  Filter,
  TrendingUp,
  Mail,
  Search,
  GraduationCap,
  Users,
  Trophy,
  RefreshCw,
  Eye,
  ChevronDown,
  Info,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAMNAContextSources, useAMNAContextCategories, useRefreshContextSource } from '@/hooks/useAMNA';
import { format } from 'date-fns';

interface ContextSource {
  id: string;
  name: string;
  icon: React.ElementType;
  dataPoints: number;
  lastSync: string;
  quality: number; // 0-100
  active: boolean;
  categories: string[];
}

interface ContextCategory {
  name: string;
  count: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
}

const sourceIcons = {
  'E-Connect': Mail,
  'Lighthouse': Search,
  'Training': GraduationCap,
  'Vendors': Users,
  'Wins': Trophy,
};

const contextSources: ContextSource[] = [
  {
    id: 'e-connect',
    name: 'E-Connect',
    icon: Mail,
    dataPoints: 12847,
    lastSync: '2 minutes ago',
    quality: 94,
    active: true,
    categories: ['Communications', 'Contacts', 'Campaigns']
  },
  {
    id: 'lighthouse',
    name: 'Lighthouse',
    icon: Search,
    dataPoints: 3456,
    lastSync: '5 minutes ago',
    quality: 89,
    active: true,
    categories: ['Research', 'Insights', 'Market Data']
  },
  {
    id: 'training',
    name: 'Training',
    icon: GraduationCap,
    dataPoints: 892,
    lastSync: '15 minutes ago',
    quality: 91,
    active: true,
    categories: ['Skills', 'Assessments', 'Progress']
  },
  {
    id: 'vendors',
    name: 'Vendors',
    icon: Users,
    dataPoints: 567,
    lastSync: '30 minutes ago',
    quality: 87,
    active: true,
    categories: ['Contracts', 'Performance', 'Capabilities']
  },
  {
    id: 'wins',
    name: 'Wins',
    icon: Trophy,
    dataPoints: 234,
    lastSync: '1 hour ago',
    quality: 95,
    active: true,
    categories: ['Achievements', 'Milestones', 'Metrics']
  }
];

const contextCategories: ContextCategory[] = [
  { name: 'Communications', count: 8234, percentage: 45, trend: 'up' },
  { name: 'Research & Insights', count: 3456, percentage: 19, trend: 'up' },
  { name: 'Training Data', count: 2890, percentage: 16, trend: 'stable' },
  { name: 'Vendor Info', count: 2145, percentage: 12, trend: 'down' },
  { name: 'Achievements', count: 1467, percentage: 8, trend: 'up' }
];

export default function ContextAggregation() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  
  const { data: apiSources, isLoading: sourcesLoading, error: sourcesError } = useAMNAContextSources();
  const { data: apiCategories, isLoading: categoriesLoading } = useAMNAContextCategories();
  const refreshSourceMutation = useRefreshContextSource();
  
  // Transform and use API data if available
  const sources: ContextSource[] = apiSources?.map(source => ({
    id: source.source.toLowerCase().replace(' ', '-'),
    name: source.source,
    icon: sourceIcons[source.source as keyof typeof sourceIcons] || Database,
    dataPoints: source.dataPoints,
    lastSync: typeof source.lastSync === 'string' 
      ? source.lastSync 
      : format(new Date(source.lastSync), 'MMM d, h:mm a'),
    quality: source.quality,
    active: source.active,
    categories: source.categories
  })) || contextSources;
  
  const categories = apiCategories || contextCategories;
  
  const refreshSource = async (sourceName: string) => {
    try {
      await refreshSourceMutation.mutateAsync(sourceName);
    } catch (error) {
      console.error('Failed to refresh source:', error);
    }
  };

  const totalDataPoints = sources.reduce((sum, source) => sum + source.dataPoints, 0);
  const averageQuality = sources.length > 0 ? Math.round(
    sources.reduce((sum, source) => sum + source.quality, 0) / sources.length
  ) : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Context Aggregation</CardTitle>
            <CardDescription>
              Real-time data collection from all integrated sources
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline">
              <Filter className="w-4 h-4 mr-1" />
              Filter
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => refreshSource('all')}
              disabled={refreshSourceMutation.isLoading}
            >
              {refreshSourceMutation.isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sources">Sources</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            {/* Summary Stats */}
            {sourcesLoading ? (
              <div className="grid grid-cols-4 gap-4 mb-6">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <Network className="w-5 h-5 mx-auto mb-1 text-primary" />
                  <p className="text-2xl font-bold">{sources.length}</p>
                  <p className="text-xs text-muted-foreground">Active Sources</p>
                </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Database className="w-5 h-5 mx-auto mb-1 text-primary" />
                <p className="text-2xl font-bold">{(totalDataPoints / 1000).toFixed(1)}k</p>
                <p className="text-xs text-muted-foreground">Data Points</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <TrendingUp className="w-5 h-5 mx-auto mb-1 text-primary" />
                <p className="text-2xl font-bold">{averageQuality}%</p>
                <p className="text-xs text-muted-foreground">Avg Quality</p>
              </div>
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <RefreshCw className="w-5 h-5 mx-auto mb-1 text-primary" />
                  <p className="text-2xl font-bold">Live</p>
                  <p className="text-xs text-muted-foreground">Sync Status</p>
                </div>
              </div>
            )}

            {/* Visual Context Map */}
            <div className="relative h-64 bg-muted/20 rounded-lg p-4 overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-48 h-48">
                  {/* Central AMNA Node */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-primary rounded-full flex items-center justify-center shadow-lg z-10">
                    <span className="text-xs font-bold text-primary-foreground">AMNA</span>
                  </div>

                  {/* Source Nodes */}
                  {sources.map((source, index) => {
                    const angle = (index * 360) / contextSources.length;
                    const radius = 80;
                    const x = radius * Math.cos((angle * Math.PI) / 180);
                    const y = radius * Math.sin((angle * Math.PI) / 180);
                    const Icon = source.icon;

                    return (
                      <motion.div
                        key={source.id}
                        className="absolute"
                        style={{
                          top: '50%',
                          left: '50%',
                          transform: `translate(${x}px, ${y}px) translate(-50%, -50%)`
                        }}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        {/* Connection Line */}
                        <svg
                          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 -z-10"
                          style={{ width: radius, height: radius }}
                        >
                          <line
                            x1="0"
                            y1="0"
                            x2={-x}
                            y2={-y}
                            stroke="currentColor"
                            strokeWidth="2"
                            className="text-muted-foreground/30"
                            strokeDasharray="5,5"
                          />
                        </svg>

                        {/* Source Node */}
                        <div
                          className={`w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-all ${
                            selectedSource === source.id
                              ? 'bg-primary shadow-lg scale-110'
                              : 'bg-background border-2'
                          }`}
                          onClick={() => setSelectedSource(source.id)}
                        >
                          <Icon className={`w-5 h-5 ${
                            selectedSource === source.id ? 'text-primary-foreground' : ''
                          }`} />
                        </div>
                        
                        <p className="text-xs mt-1 text-center whitespace-nowrap">
                          {source.name}
                        </p>
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Data Flow Animation */}
              <AnimatePresence>
                {sources.map((source, index) => (
                  <motion.div
                    key={`flow-${source.id}`}
                    className="absolute w-2 h-2 bg-primary rounded-full"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      delay: index * 0.4,
                      repeat: Infinity,
                    }}
                    style={{
                      top: '50%',
                      left: '50%',
                    }}
                  />
                ))}
              </AnimatePresence>
            </div>
          </TabsContent>

          <TabsContent value="sources" className="mt-6">
            <div className="space-y-3">
              {sourcesLoading ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-24 w-full" />
                ))
              ) : sourcesError ? (
                <div className="text-center py-8 text-muted-foreground">
                  <AlertCircle className="w-8 h-8 mx-auto mb-2 text-destructive" />
                  <p>Failed to load context sources</p>
                </div>
              ) : (
                sources.map((source) => {
                const Icon = source.icon;
                return (
                  <div
                    key={source.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all ${
                      selectedSource === source.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => setSelectedSource(source.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-muted rounded-lg">
                          <Icon className="w-5 h-5" />
                        </div>
                        <div>
                          <h4 className="font-medium">{source.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {source.dataPoints.toLocaleString()} data points
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm">Quality</span>
                          <Progress value={source.quality} className="w-20 h-2" />
                          <span className="text-sm font-medium">{source.quality}%</span>
                        </div>
                        <p className="text-xs text-muted-foreground">{source.lastSync}</p>
                      </div>
                    </div>

                    {selectedSource === source.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="mt-4 pt-4 border-t"
                      >
                        <div className="flex flex-wrap gap-2">
                          {source.categories.map((cat) => (
                            <Badge key={cat} variant="secondary">{cat}</Badge>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                );
                })
              )}
            </div>
          </TabsContent>

          <TabsContent value="categories" className="mt-6">
            <div className="space-y-3">
              {categoriesLoading ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))
              ) : (
                categories.map((category) => (
                <div key={category.name} className="p-3 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{category.name}</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant={category.trend === 'up' ? 'default' : category.trend === 'down' ? 'destructive' : 'secondary'}>
                        {category.trend === 'up' ? '↑' : category.trend === 'down' ? '↓' : '→'} {category.percentage}%
                      </Badge>
                      <span className="text-sm font-medium">{category.count.toLocaleString()}</span>
                    </div>
                  </div>
                  <Progress value={category.percentage} className="h-2" />
                </div>
                ))
              )}
            </div>

            <Button
              variant="outline"
              className="w-full mt-4"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              <Eye className="w-4 h-4 mr-2" />
              {showDetails ? 'Hide' : 'Show'} Detailed Analysis
              <ChevronDown className={`w-4 h-4 ml-1 transition-transform ${showDetails ? 'rotate-180' : ''}`} />
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}