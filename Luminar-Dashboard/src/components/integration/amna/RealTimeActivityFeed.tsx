'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  Brain, 
  Zap, 
  MessageSquare, 
  Search,
  Mail,
  Users,
  GraduationCap,
  Trophy,
  AlertCircle,
  CheckCircle,
  Clock,
  Pause,
  Play,
  Filter,
  Download
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { useIntegrationStore } from '@/store/integration/integrationStore';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  timestamp: Date;
  type: 'query' | 'insight' | 'automation' | 'sync' | 'alert' | 'achievement';
  source: string;
  title: string;
  description?: string;
  status?: 'success' | 'warning' | 'error' | 'info';
  metadata?: Record<string, any>;
}

const typeIcons = {
  query: MessageSquare,
  insight: Brain,
  automation: Zap,
  sync: Activity,
  alert: AlertCircle,
  achievement: Trophy,
};

const sourceIcons = {
  'amna': Brain,
  'e-connect': Mail,
  'lighthouse': Search,
  'training': GraduationCap,
  'vendors': Users,
  'wins': Trophy,
};

const statusColors = {
  success: 'text-green-500 bg-green-100 dark:bg-green-900/30',
  warning: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/30',
  error: 'text-red-500 bg-red-100 dark:bg-red-900/30',
  info: 'text-blue-500 bg-blue-100 dark:bg-blue-900/30',
};

export default function RealTimeActivityFeed() {
  const [isPaused, setIsPaused] = useState(false);
  const [filter, setFilter] = useState<string>('all');
  const scrollRef = useRef<HTMLDivElement>(null);
  const activities = useIntegrationStore((state) => state.activities);
  
  // Auto-scroll to bottom when new activities arrive (unless paused)
  useEffect(() => {
    if (!isPaused && scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [activities, isPaused]);

  // Filter activities
  const filteredActivities = filter === 'all' 
    ? activities 
    : activities.filter(activity => activity.type === filter);

  // Get last 50 activities, reversed to show newest at bottom
  const displayActivities = filteredActivities.slice(-50).reverse();

  const exportActivities = () => {
    const data = displayActivities.map(activity => ({
      timestamp: activity.timestamp,
      type: activity.type,
      source: activity.source,
      title: activity.title,
      description: activity.description,
      status: activity.metadata?.status,
    }));
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `amna-activities-${format(new Date(), 'yyyy-MM-dd-HHmmss')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getActivityIcon = (activity: ActivityItem) => {
    const TypeIcon = typeIcons[activity.type] || Activity;
    const SourceIcon = sourceIcons[activity.source as keyof typeof sourceIcons] || Activity;
    return activity.source === 'amna' ? TypeIcon : SourceIcon;
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Real-Time Activity Feed</CardTitle>
            <CardDescription>
              Live stream of AMNA intelligence activities
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsPaused(!isPaused)}
            >
              {isPaused ? (
                <Play className="w-4 h-4" />
              ) : (
                <Pause className="w-4 h-4" />
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={exportActivities}
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        {/* Filter Pills */}
        <div className="px-6 py-3 border-b">
          <div className="flex items-center gap-2 overflow-x-auto">
            <Badge
              variant={filter === 'all' ? 'default' : 'outline'}
              className="cursor-pointer"
              onClick={() => setFilter('all')}
            >
              All ({activities.length})
            </Badge>
            {Object.keys(typeIcons).map((type) => {
              const count = activities.filter(a => a.type === type).length;
              return (
                <Badge
                  key={type}
                  variant={filter === type ? 'default' : 'outline'}
                  className="cursor-pointer whitespace-nowrap"
                  onClick={() => setFilter(type)}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)} ({count})
                </Badge>
              );
            })}
          </div>
        </div>

        {/* Activity Stream */}
        <ScrollArea className="h-[400px]" ref={scrollRef}>
          <div className="p-4 space-y-2">
            <AnimatePresence initial={false}>
              {displayActivities.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="w-8 h-8 mx-auto mb-2" />
                  <p>No activities yet. Waiting for AMNA operations...</p>
                </div>
              ) : (
                displayActivities.map((activity, index) => {
                  const Icon = getActivityIcon(activity);
                  const status = activity.metadata?.status || 'info';
                  
                  return (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className={cn(
                        "flex items-start gap-3 p-3 rounded-lg border",
                        "hover:bg-muted/50 transition-colors"
                      )}
                    >
                      <div className={cn("p-2 rounded-lg", statusColors[status])}>
                        <Icon className="w-4 h-4" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <p className="font-medium text-sm truncate">
                              {activity.title}
                            </p>
                            {activity.description && (
                              <p className="text-xs text-muted-foreground mt-0.5 line-clamp-2">
                                {activity.description}
                              </p>
                            )}
                          </div>
                          <time className="text-xs text-muted-foreground whitespace-nowrap">
                            {format(new Date(activity.timestamp), 'HH:mm:ss')}
                          </time>
                        </div>
                        
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {activity.type}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            via {activity.source}
                          </span>
                          {activity.metadata?.executionTime && (
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {activity.metadata.executionTime}ms
                            </span>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              )}
            </AnimatePresence>
          </div>
        </ScrollArea>

        {/* Status Bar */}
        {!isPaused && displayActivities.length > 0 && (
          <div className="px-6 py-3 border-t bg-muted/50">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-xs text-muted-foreground">
                Live - Streaming activities in real-time
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}