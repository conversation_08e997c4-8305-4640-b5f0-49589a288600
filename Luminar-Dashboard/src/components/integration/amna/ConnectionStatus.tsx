'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw,
  AlertCircle 
} from 'lucide-react';
import { useAMNAWebSocket } from './AMNAWebSocketProvider';
import { cn } from '@/lib/utils';

export default function ConnectionStatus() {
  const { isConnected, reconnect } = useAMNAWebSocket();

  return (
    <div className="flex items-center gap-2">
      <Badge 
        variant={isConnected ? 'default' : 'destructive'}
        className={cn(
          "flex items-center gap-1.5",
          isConnected && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
        )}
      >
        {isConnected ? (
          <>
            <Wifi className="w-3 h-3" />
            <span>Connected</span>
            <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
          </>
        ) : (
          <>
            <WifiOff className="w-3 h-3" />
            <span>Disconnected</span>
          </>
        )}
      </Badge>
      
      {!isConnected && (
        <Button
          size="sm"
          variant="outline"
          onClick={reconnect}
          className="h-7"
        >
          <RefreshCw className="w-3 h-3 mr-1" />
          Reconnect
        </Button>
      )}
    </div>
  );
}