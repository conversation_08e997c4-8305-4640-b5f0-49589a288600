groups:
  - name: integration_alerts
    interval: 30s
    rules:
      # Service availability
      - alert: IntegrationServiceDown
        expr: up{job=~"e-connect|lighthouse|training|vendors|wins"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Integration service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute."

      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.service }}"
          description: "Error rate is {{ humanize $value }}% for {{ $labels.service }}"

      # Slow response time
      - alert: SlowResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "95th percentile response time > 1s"
          description: "Response time is {{ humanize $value }}s for {{ $labels.service }}"

      # Privacy request backlog
      - alert: PrivacyRequestBacklog
        expr: privacy_requests_active > 50
        for: 10m
        labels:
          severity: warning
          category: privacy
        annotations:
          summary: "High number of active privacy requests"
          description: "{{ $value }} privacy requests are pending processing"

      # Compliance score drop
      - alert: ComplianceScoreDrop
        expr: compliance_score_by_category < 85
        for: 30m
        labels:
          severity: critical
          category: compliance
        annotations:
          summary: "Compliance score below threshold"
          description: "{{ $labels.category }} compliance score is {{ $value }}%"

      # Low user engagement
      - alert: LowUserEngagement
        expr: user_engagement_rate < 40
        for: 1h
        labels:
          severity: info
          category: business
        annotations:
          summary: "User engagement rate is low"
          description: "Current engagement rate is {{ $value }}%"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ humanizePercentage $value }}"

      # Database connection pool exhaustion
      - alert: DatabaseConnectionPoolExhaustion
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "{{ humanizePercentage $value }} of connections are in use"

      # WebSocket connection spike
      - alert: WebSocketConnectionSpike
        expr: rate(websocket_connections_active[5m]) > 100
        for: 5m
        labels:
          severity: info
          category: realtime
        annotations:
          summary: "Spike in WebSocket connections"
          description: "WebSocket connection rate is {{ $value }} per second"

      # Privacy incident detected
      - alert: PrivacyIncidentDetected
        expr: increase(privacy_incidents_total[1h]) > 0
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Privacy incident detected"
          description: "{{ $value }} privacy incidents in the last hour"