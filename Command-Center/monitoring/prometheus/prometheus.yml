global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'luminar-production'
    environment: 'production'

# Alert manager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

# Load rules
rule_files:
  - "/etc/prometheus/rules/*.yml"

# Scrape configurations
scrape_configs:
  # Luminar Integration Service
  - job_name: 'luminar-integration'
    static_configs:
      - targets: ['command-center:3000']
        labels:
          service: 'command-center'
          component: 'integration'
    metrics_path: '/api/monitoring/metrics'

  # E-Connect Service
  - job_name: 'e-connect'
    static_configs:
      - targets: ['e-connect:3001']
        labels:
          service: 'e-connect'
          integration: 'email'
    metrics_path: '/metrics'

  # Lighthouse Service
  - job_name: 'lighthouse'
    static_configs:
      - targets: ['lighthouse:3002']
        labels:
          service: 'lighthouse'
          integration: 'research'
    metrics_path: '/metrics'

  # Training Service
  - job_name: 'training'
    static_configs:
      - targets: ['training:3003']
        labels:
          service: 'training'
          integration: 'learning'
    metrics_path: '/metrics'

  # Vendors Service
  - job_name: 'vendors'
    static_configs:
      - targets: ['vendors:3004']
        labels:
          service: 'vendors'
          integration: 'procurement'
    metrics_path: '/metrics'

  # Wins Service
  - job_name: 'wins'
    static_configs:
      - targets: ['wins:3005']
        labels:
          service: 'wins'
          integration: 'achievements'
    metrics_path: '/metrics'

  # AMNA Service
  - job_name: 'amna'
    static_configs:
      - targets: ['amna:3000']
        labels:
          service: 'amna'
          component: 'chat-interface'
    metrics_path: '/metrics'

  # Node Exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          component: 'infrastructure'

  # PostgreSQL Exporter
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres-exporter:9187']
        labels:
          component: 'database'

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
        labels:
          component: 'cache'

  # RabbitMQ Exporter
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq-exporter:9419']
        labels:
          component: 'message-queue'