# Luminar Integration Monitoring

This directory contains the monitoring stack configuration for the Luminar Integration system.

## Overview

The monitoring stack includes:
- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and dashboards
- **AlertManager**: Alert routing and notification
- **Various Exporters**: Infrastructure and application metrics

## Quick Start

1. Start the monitoring stack:
```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

2. Access the services:
- Grafana: http://localhost:3001 (admin/admin)
- Prometheus: http://localhost:9090
- AlertManager: http://localhost:9093

## Dashboards

Three pre-configured dashboards are available:

### 1. Integration Overview Dashboard
- Real-time integration status
- Request rates by application
- WebSocket connections
- Error rates and response times

### 2. Business Metrics Dashboard
- Daily/Weekly Active Users
- User engagement rates
- Feature adoption metrics
- Revenue and cost savings tracking
- Customer satisfaction scores

### 3. Privacy & Compliance Dashboard
- GDPR request tracking
- Consent management metrics
- Compliance scores by category
- Privacy incident monitoring

## Metrics

### Business Metrics
- `user_activity_total`: Total user activities
- `active_users_daily/weekly`: Active user counts
- `user_engagement_rate`: Engagement percentage
- `revenue_generated_total`: Revenue tracking
- `cost_savings_total`: Cost savings metrics
- `efficiency_improvement_percentage`: Process efficiency
- `user_satisfaction_score`: Customer satisfaction

### Privacy Metrics
- `privacy_requests_total`: Privacy request counts
- `privacy_requests_active`: Active request gauge
- `consent_granted/withdrawn_total`: Consent metrics
- `compliance_score_by_category`: Compliance scoring
- `privacy_incidents_total`: Incident tracking

### Integration Metrics
- `integration_activities_total`: Activity counts
- `integration_requests_total`: Request metrics
- `websocket_connections_active`: WebSocket monitoring
- `amna_messages_processed_total`: AMNA message processing

## Alerts

Pre-configured alerts include:
- Service availability monitoring
- High error rate detection
- Slow response time alerts
- Privacy request backlog warnings
- Compliance score monitoring
- Resource utilization alerts

## Custom Metrics

To add custom metrics in your application:

```typescript
@Injectable()
export class MyService {
  constructor(
    @InjectMetric('my_custom_metric') private myMetric: Counter<string>,
  ) {}

  trackEvent() {
    this.myMetric.inc({ label: 'value' });
  }
}
```

## Alert Configuration

Alerts are configured in `prometheus/rules/integration-alerts.yml`. To add new alerts:

```yaml
- alert: MyCustomAlert
  expr: my_metric > 100
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Custom alert triggered"
    description: "Value is {{ $value }}"
```

## Grafana Variables

Dashboards support these variables:
- `$datasource`: Prometheus data source
- `$interval`: Time interval for aggregation
- `$service`: Filter by service name
- `$user_id`: Filter by user ID

## Backup and Restore

### Backup Grafana Dashboards
```bash
# Export dashboards
curl -u admin:admin http://localhost:3001/api/dashboards/db/integration-overview > dashboard-backup.json
```

### Backup Prometheus Data
```bash
# Create snapshot
curl -XPOST http://localhost:9090/api/v1/admin/tsdb/snapshot

# Copy snapshot data
docker cp luminar-prometheus:/prometheus/snapshots ./prometheus-backup
```

## Troubleshooting

### No metrics appearing
1. Check if services are exposing metrics endpoint
2. Verify Prometheus scrape configuration
3. Check network connectivity between services

### Dashboards not loading
1. Ensure Grafana provisioning is correct
2. Check datasource configuration
3. Verify dashboard JSON syntax

### Alerts not firing
1. Check alert rule syntax
2. Verify AlertManager configuration
3. Check notification channels

## Integration with Application

The application automatically tracks metrics through event listeners:

```typescript
// User activity tracking
this.eventEmitter.emit('user.activity', {
  userId: 'user123',
  activity: 'login',
  source: 'web',
});

// Privacy request tracking
this.eventEmitter.emit('privacy.request.created', {
  request: { type: 'data_export', userId: 'user123' },
  timestamp: new Date(),
});
```

## Performance Considerations

- Prometheus retention: 15 days by default
- Scrape interval: 15 seconds
- Dashboard refresh: 10 seconds
- Alert evaluation: 30 seconds

## Security

- Enable authentication for all services
- Use HTTPS in production
- Restrict network access
- Rotate credentials regularly

## Maintenance

### Daily Tasks
- Check alert status
- Monitor dashboard health
- Review error rates

### Weekly Tasks
- Review metric cardinality
- Check storage usage
- Update alert thresholds

### Monthly Tasks
- Dashboard optimization
- Alert rule review
- Backup verification