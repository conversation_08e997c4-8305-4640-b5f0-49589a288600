[{"key": "integration.e-connect.enabled", "name": "E-Connect Integration", "description": "Enable E-Connect email integration", "defaultValue": true, "enabled": true, "tags": ["integration", "email"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.lighthouse.enabled", "name": "Lighthouse Integration", "description": "Enable Lighthouse research integration", "defaultValue": true, "enabled": true, "tags": ["integration", "research"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.training.enabled", "name": "Training Integration", "description": "Enable Training integration", "defaultValue": true, "enabled": true, "tags": ["integration", "training"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.vendors.enabled", "name": "Vendors Integration", "description": "Enable Vendors integration", "defaultValue": true, "enabled": true, "tags": ["integration", "vendors"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.wins.enabled", "name": "Wins Integration", "description": "Enable Wins-of-Week integration", "defaultValue": true, "enabled": true, "tags": ["integration", "wins"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.realtime-sync.enabled", "name": "Real-time Sync", "description": "Enable real-time data synchronization", "defaultValue": false, "enabled": true, "tags": ["feature", "performance"], "rollout": {"variations": [{"variation": "false", "weight": 50}, {"variation": "true", "weight": 50}]}, "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.ai-insights.enabled", "name": "AI Insights", "description": "Enable AI-powered insights", "defaultValue": false, "enabled": true, "tags": ["feature", "ai"], "rollout": {"variations": [{"variation": "false", "weight": 80}, {"variation": "true", "weight": 20}]}, "rules": [{"id": "beta-users", "clauses": [{"attribute": "roles", "operator": "in", "values": ["beta", "admin"]}], "variation": "true"}], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.advanced-analytics.enabled", "name": "Advanced Analytics", "description": "Enable advanced analytics features", "defaultValue": false, "enabled": true, "tags": ["feature", "analytics"], "targets": [{"variation": "true", "users": ["user-123", "user-456"], "groups": ["enterprise"]}], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.circuit-breaker.enabled", "name": "Circuit Breaker", "description": "Enable circuit breaker pattern for external services", "defaultValue": true, "enabled": true, "tags": ["reliability", "performance"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.sync.batch-size", "name": "Sync Batch Size", "description": "Number of items to sync in a single batch", "defaultValue": 100, "enabled": true, "tags": ["performance", "configuration"], "variations": [{"id": "small", "value": 50, "name": "Small batch"}, {"id": "medium", "value": 100, "name": "Medium batch"}, {"id": "large", "value": 200, "name": "Large batch"}], "rules": [{"id": "high-volume-users", "clauses": [{"attribute": "attributes.activityCount", "operator": "greaterThan", "values": [1000]}], "variation": "large"}], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.sync.timeout", "name": "Sync Timeout", "description": "Timeout for sync operations in milliseconds", "defaultValue": 30000, "enabled": true, "tags": ["performance", "configuration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.cache.config", "name": "<PERSON><PERSON> Configuration", "description": "Cache configuration settings", "defaultValue": {"enabled": true, "ttl": 300, "maxSize": 1000}, "enabled": true, "tags": ["performance", "configuration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.e-connect.rollout", "name": "E-Connect Rollout Percentage", "description": "Percentage of users with E-Connect integration enabled", "defaultValue": 100, "enabled": true, "tags": ["rollout", "integration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.lighthouse.rollout", "name": "Lighthouse Rollout Percentage", "description": "Percentage of users with Lighthouse integration enabled", "defaultValue": 100, "enabled": true, "tags": ["rollout", "integration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.training.rollout", "name": "Training Rollout Percentage", "description": "Percentage of users with Training integration enabled", "defaultValue": 100, "enabled": true, "tags": ["rollout", "integration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.vendors.rollout", "name": "Vendors Rollout Percentage", "description": "Percentage of users with Vendors integration enabled", "defaultValue": 100, "enabled": true, "tags": ["rollout", "integration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"key": "integration.wins.rollout", "name": "Wins Rollout Percentage", "description": "Percentage of users with Wins integration enabled", "defaultValue": 100, "enabled": true, "tags": ["rollout", "integration"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}]