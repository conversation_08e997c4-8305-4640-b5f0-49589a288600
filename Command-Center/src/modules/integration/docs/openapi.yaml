openapi: 3.0.3
info:
  title: Luminar Integration API
  description: |
    The Luminar Integration API provides a unified interface for managing cross-application
    integrations within the Luminar ecosystem. It enables seamless data flow between:
    
    - **AMNA**: AI-powered assistant providing intelligent insights
    - **E-Connect**: Email automation and communication platform
    - **Lighthouse**: Research and knowledge management system
    - **Training**: Skill assessment and learning management
    - **Vendors**: Vendor relationship and contract management
    - **Wins-of-Week**: Achievement tracking and reporting
    
    ## Key Features
    
    - Real-time activity tracking across applications
    - Intelligent data federation with AI insights
    - Configurable user preferences and privacy controls
    - WebSocket support for real-time updates
    - GDPR-compliant data management
    - Advanced rate limiting and circuit breaker protection
    
    ## Authentication
    
    The API uses JWT Bearer tokens for authentication. Include the token in the
    Authorization header: `Authorization: Bearer <token>`
    
    ## Rate Limiting
    
    Different endpoints have different rate limits:
    - Standard endpoints: 60 requests/minute
    - Sensitive operations: 10 requests/minute
    - WebSocket messages: 20 messages/second
    
    Rate limit information is included in response headers.
    
  version: 1.0.0
  contact:
    name: Luminar Support
    email: <EMAIL>
  license:
    name: Proprietary
    
servers:
  - url: https://api.luminar.com
    description: Production server
  - url: https://staging-api.luminar.com
    description: Staging server
  - url: http://localhost:3000
    description: Development server
    
security:
  - bearerAuth: []
    
tags:
  - name: Activities
    description: Manage integration activities across applications
  - name: Preferences
    description: User integration preferences and settings
  - name: Synchronization
    description: Data synchronization operations
  - name: Federation
    description: Cross-application data federation
  - name: Health
    description: System health and monitoring
  - name: GDPR
    description: GDPR compliance operations
    
paths:
  /api/integration/activity:
    post:
      tags:
        - Activities
      summary: Create integration activity
      description: Records a new activity from any integrated application
      operationId: createActivity
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateActivityDto'
            examples:
              emailActivity:
                summary: Email activity
                value:
                  userId: "user-123"
                  source: "e-connect"
                  type: "email.sent"
                  data:
                    to: "<EMAIL>"
                    subject: "Project Update"
              trainingActivity:
                summary: Training activity
                value:
                  userId: "user-123"
                  source: "training"
                  type: "skill.gap.identified"
                  data:
                    skill: "Cloud Architecture"
                    currentLevel: 2
                    requiredLevel: 4
      responses:
        '201':
          description: Activity created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActivityResponseDto'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
          
  /api/integration/activities/{userId}:
    get:
      tags:
        - Activities
      summary: Get user activities
      description: Retrieves activities for a specific user with filtering options
      operationId: getActivities
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
        - name: source
          in: query
          description: Filter by source application
          schema:
            $ref: '#/components/schemas/IntegrationSource'
        - name: type
          in: query
          description: Filter by activity type
          schema:
            type: string
        - name: startDate
          in: query
          description: Start date for filtering
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          description: End date for filtering
          schema:
            type: string
            format: date-time
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/OffsetParam'
      responses:
        '200':
          description: Activities retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  activities:
                    type: array
                    items:
                      $ref: '#/components/schemas/ActivityResponseDto'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer
                    
  /api/integration/preferences/{userId}:
    get:
      tags:
        - Preferences
      summary: Get user preferences
      description: Retrieves integration preferences for a user
      operationId: getPreferences
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: Preferences retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreferencesResponseDto'
        '404':
          $ref: '#/components/responses/NotFound'
          
    put:
      tags:
        - Preferences
      summary: Update user preferences
      description: Updates integration preferences for a user
      operationId: updatePreferences
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePreferencesDto'
      responses:
        '200':
          description: Preferences updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreferencesResponseDto'
                
  /api/integration/sync/{userId}:
    post:
      tags:
        - Synchronization
      summary: Sync user data
      description: Triggers data synchronization across applications
      operationId: syncUserData
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncRequestDto'
      responses:
        '200':
          description: Sync initiated
          content:
            application/json:
              schema:
                type: object
                properties:
                  syncId:
                    type: string
                  status:
                    type: string
                  applications:
                    type: array
                    items:
                      type: string
                  estimatedTime:
                    type: string
                    
  /api/integration/health:
    get:
      tags:
        - Health
      summary: Health check
      description: Comprehensive system health check
      operationId: healthCheck
      security: []
      responses:
        '200':
          description: System healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatusResponseDto'
        '503':
          description: System unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatusResponseDto'
                
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      
  parameters:
    UserIdParam:
      name: userId
      in: path
      required: true
      description: User ID
      schema:
        type: string
        example: user-123
        
    LimitParam:
      name: limit
      in: query
      description: Maximum number of results
      schema:
        type: integer
        minimum: 1
        maximum: 1000
        default: 50
        
    OffsetParam:
      name: offset
      in: query
      description: Offset for pagination
      schema:
        type: integer
        minimum: 0
        default: 0
        
  schemas:
    IntegrationSource:
      type: string
      enum:
        - e-connect
        - lighthouse
        - training
        - vendors
        - wins-of-week
        - amna
        
    ActivityType:
      type: string
      enum:
        - email.sent
        - email.received
        - research.completed
        - insight.generated
        - skill.gap.identified
        - training.completed
        - vendor.matched
        - contract.signed
        - achievement.unlocked
        
    CreateActivityDto:
      type: object
      required:
        - userId
        - source
        - type
        - data
      properties:
        userId:
          type: string
        source:
          $ref: '#/components/schemas/IntegrationSource'
        type:
          $ref: '#/components/schemas/ActivityType'
        data:
          type: object
        timestamp:
          type: string
          format: date-time
          
    ActivityResponseDto:
      allOf:
        - $ref: '#/components/schemas/CreateActivityDto'
        - type: object
          properties:
            id:
              type: string
            processed:
              type: boolean
            insights:
              type: array
              items:
                type: string
                
    UpdatePreferencesDto:
      type: object
      properties:
        e_connect_integration:
          type: boolean
        lighthouse_integration:
          type: boolean
        training_integration:
          type: boolean
        vendor_integration:
          type: boolean
        wins_tracking:
          type: boolean
        real_time_sync:
          type: boolean
        sync_frequency:
          type: string
          enum: [immediate, hourly, daily, weekly]
        intelligence_level:
          type: string
          enum: [basic, standard, advanced]
          
    PreferencesResponseDto:
      allOf:
        - $ref: '#/components/schemas/UpdatePreferencesDto'
        - type: object
          required:
            - id
            - created_at
            - updated_at
          properties:
            id:
              type: string
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
              
    SyncRequestDto:
      type: object
      properties:
        applications:
          type: array
          items:
            $ref: '#/components/schemas/IntegrationSource'
        force:
          type: boolean
          
    HealthStatusResponseDto:
      type: object
      properties:
        overall:
          type: string
          enum: [healthy, degraded, unhealthy]
        services:
          type: object
        metrics:
          type: object
        timestamp:
          type: string
          format: date-time
          
    ErrorResponseDto:
      type: object
      properties:
        statusCode:
          type: integer
        message:
          type: string
        error:
          type: string
        timestamp:
          type: string
          format: date-time
          
  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponseDto'
            
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponseDto'
            
    NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponseDto'
            
    RateLimitExceeded:
      description: Rate Limit Exceeded
      headers:
        X-RateLimit-Limit:
          schema:
            type: integer
        X-RateLimit-Remaining:
          schema:
            type: integer
        X-RateLimit-Reset:
          schema:
            type: string
            format: date-time
        Retry-After:
          schema:
            type: integer
      content:
        application/json:
          schema:
            type: object
            allOf:
              - $ref: '#/components/schemas/ErrorResponseDto'
              - properties:
                  retryAfter:
                    type: integer
                  limit:
                    type: integer
                  remainingRequests:
                    type: integer