# Integration Service Monitoring and Alerts Runbook

## Overview
This runbook defines monitoring strategies, alert configurations, and response procedures for the Luminar Integration Service.

## Key Performance Indicators (KPIs)

### Service Level Objectives (SLOs)
- **Availability**: 99.9% uptime (43.2 minutes downtime/month)
- **Latency**: 95% of requests < 200ms, 99% < 500ms
- **Error Rate**: < 0.1% of total requests
- **WebSocket Stability**: 99% connection success rate

### Service Level Indicators (SLIs)
```yaml
# Availability SLI
availability_sli: |
  sum(rate(http_requests_total{service="integration",status!~"5.."}[5m])) /
  sum(rate(http_requests_total{service="integration"}[5m]))

# Latency SLI
latency_sli: |
  histogram_quantile(0.95, 
    sum(rate(http_request_duration_seconds_bucket{service="integration"}[5m])) 
    by (le)
  )

# Error Rate SLI
error_rate_sli: |
  sum(rate(http_requests_total{service="integration",status=~"5.."}[5m])) /
  sum(rate(http_requests_total{service="integration"}[5m]))
```

## Alert Configurations

### Critical Alerts (Page immediately)

#### 1. Service Down
```yaml
alert: IntegrationServiceDown
expr: up{job="integration"} == 0
for: 1m
labels:
  severity: critical
  team: platform
annotations:
  summary: "Integration service is down"
  description: "Integration service has been down for more than 1 minute"
  runbook: "https://wiki.luminar.com/runbooks/integration-down"
```

**Response**:
1. Check pod status: `kubectl get pods -n production | grep integration`
2. Check recent deployments: `kubectl rollout history deployment/integration`
3. Check logs: `kubectl logs deployment/integration --tail=100`
4. Rollback if needed: `kubectl rollout undo deployment/integration`

#### 2. High Error Rate
```yaml
alert: IntegrationHighErrorRate
expr: |
  sum(rate(http_requests_total{service="integration",status=~"5.."}[5m])) /
  sum(rate(http_requests_total{service="integration"}[5m])) > 0.05
for: 5m
labels:
  severity: critical
annotations:
  summary: "High error rate detected"
  description: "Error rate is {{ $value | humanizePercentage }} over the last 5 minutes"
```

**Response**:
1. Check error logs: `./scripts/analyze-errors.sh --last=10m`
2. Check circuit breaker status
3. Verify external service connectivity
4. Scale up if load-related

#### 3. Database Connection Pool Exhausted
```yaml
alert: DatabasePoolExhausted
expr: pg_stat_database_numbackends{db="luminar"} / pg_settings_max_connections > 0.9
for: 2m
labels:
  severity: critical
annotations:
  summary: "Database connection pool nearly exhausted"
  description: "{{ $value | humanizePercentage }} of connections in use"
```

### High Priority Alerts (Page during business hours)

#### 1. High Latency
```yaml
alert: IntegrationHighLatency
expr: |
  histogram_quantile(0.95, 
    sum(rate(http_request_duration_seconds_bucket{service="integration"}[5m])) 
    by (le)
  ) > 0.5
for: 10m
labels:
  severity: high
annotations:
  summary: "High latency detected"
  description: "P95 latency is {{ $value }}s"
```

#### 2. Queue Backup
```yaml
alert: IntegrationQueueBackup
expr: redis_list_length{key="bull:integration-sync:wait"} > 1000
for: 5m
labels:
  severity: high
annotations:
  summary: "Integration queue backing up"
  description: "{{ $value }} jobs in queue"
```

#### 3. Circuit Breaker Open
```yaml
alert: CircuitBreakerOpen
expr: circuit_breaker_state{service="integration"} == 2
for: 1m
labels:
  severity: high
annotations:
  summary: "Circuit breaker open for {{ $labels.dependency }}"
  description: "Circuit breaker has been open for 1 minute"
```

### Medium Priority Alerts (Slack notification)

#### 1. Memory Usage
```yaml
alert: IntegrationHighMemory
expr: |
  container_memory_usage_bytes{pod=~"integration-.*"} /
  container_spec_memory_limit_bytes{pod=~"integration-.*"} > 0.8
for: 10m
labels:
  severity: medium
annotations:
  summary: "High memory usage"
  description: "Memory usage is {{ $value | humanizePercentage }}"
```

#### 2. Disk Usage
```yaml
alert: IntegrationHighDiskUsage
expr: |
  (node_filesystem_size_bytes{mountpoint="/"} - node_filesystem_free_bytes{mountpoint="/"}) /
  node_filesystem_size_bytes{mountpoint="/"} > 0.85
for: 10m
labels:
  severity: medium
```

## Monitoring Dashboards

### Grafana Dashboards

#### 1. Integration Overview Dashboard
- URL: https://grafana.luminar.com/d/integration-overview
- Panels:
  - Request rate (requests/sec)
  - Error rate (%)
  - Latency (P50, P95, P99)
  - Active users
  - WebSocket connections

#### 2. Performance Dashboard
- URL: https://grafana.luminar.com/d/integration-performance
- Panels:
  - Response time distribution
  - Database query performance
  - Cache hit rates
  - Queue processing times
  - CPU and memory usage

#### 3. Business Metrics Dashboard
- URL: https://grafana.luminar.com/d/integration-business
- Panels:
  - Activities by source
  - Cross-app workflows
  - User engagement
  - Integration adoption
  - Feature usage

### Custom Queries

#### Activity Metrics
```promql
# Activities per minute by source
sum(rate(integration_activity_created_total[1m])) by (source)

# Cross-app workflow success rate
sum(rate(integration_workflow_completed_total[5m])) /
sum(rate(integration_workflow_started_total[5m]))

# Average sync duration
histogram_quantile(0.95,
  sum(rate(integration_sync_duration_seconds_bucket[5m])) by (le)
)
```

#### Performance Metrics
```promql
# Cache hit rate
sum(rate(cache_hits_total[5m])) /
(sum(rate(cache_hits_total[5m])) + sum(rate(cache_misses_total[5m])))

# WebSocket connection churn
sum(rate(websocket_connections_closed_total[5m]))

# Queue processing rate
sum(rate(queue_jobs_completed_total[1m])) by (queue)
```

## Alert Response Procedures

### Escalation Matrix
| Severity | Response Time | Escalation Path | Communication |
|----------|--------------|-----------------|---------------|
| Critical | < 5 minutes | On-call → Team Lead → CTO | Page + Slack + Email |
| High | < 30 minutes | On-call → Team Lead | Page (business hours) + Slack |
| Medium | < 2 hours | Team Slack | Slack notification |
| Low | < 24 hours | Team backlog | JIRA ticket |

### On-Call Responsibilities
1. **Acknowledge** alert within 5 minutes
2. **Assess** severity and impact
3. **Communicate** status in #incidents channel
4. **Mitigate** immediate issues
5. **Escalate** if needed
6. **Document** actions taken

### Incident Response Template
```markdown
## Incident: [Alert Name]
**Time**: [Timestamp]
**Severity**: [Critical/High/Medium]
**Impact**: [User-facing impact]

### Current Status
- [ ] Acknowledged
- [ ] Investigating
- [ ] Mitigating
- [ ] Resolved

### Timeline
- HH:MM - Alert triggered
- HH:MM - [Action taken]
- HH:MM - [Status update]

### Root Cause
[Brief description]

### Resolution
[Steps taken to resolve]

### Follow-up
- [ ] Post-mortem scheduled
- [ ] Monitoring improved
- [ ] Documentation updated
```

## Proactive Monitoring

### Daily Checks
```bash
# Morning health check
./scripts/daily-health-check.sh

# Check for anomalies
./scripts/detect-anomalies.sh --window=24h

# Review error trends
./scripts/error-trend-analysis.sh
```

### Weekly Reviews
1. Review SLO performance
2. Analyze capacity trends
3. Update alert thresholds
4. Review false positives

### Monthly Tasks
1. Load testing
2. Disaster recovery drill
3. Alert audit
4. Dashboard optimization

## Tools and Resources

### Monitoring Tools
- **Prometheus**: https://prometheus.luminar.com
- **Grafana**: https://grafana.luminar.com
- **Jaeger**: https://jaeger.luminar.com
- **Kibana**: https://kibana.luminar.com

### Useful Commands
```bash
# Get current metrics
curl https://api.luminar.com/metrics

# Test alert
amtool alert add IntegrationTest severity=medium

# Silence alerts during maintenance
amtool silence add alertname="Integration.*" --duration="1h" --comment="Maintenance"

# Query Prometheus
promtool query instant 'up{job="integration"}'
```

### Alert Testing
```bash
# Simulate high error rate
./scripts/simulate-errors.sh --rate=10 --duration=5m

# Simulate high latency
./scripts/simulate-latency.sh --delay=1000ms

# Simulate service down
kubectl scale deployment/integration --replicas=0
```

## Best Practices

### Alert Design
1. **Actionable**: Every alert should have clear actions
2. **Symptom-based**: Alert on user impact, not just metrics
3. **Tested**: Regularly test alerts work correctly
4. **Documented**: Include runbook links in alerts

### Noise Reduction
1. Use appropriate thresholds
2. Implement alert deduplication
3. Group related alerts
4. Use time-based suppression

### Continuous Improvement
1. Weekly alert review
2. Post-incident analysis
3. Regular threshold tuning
4. Automation of responses