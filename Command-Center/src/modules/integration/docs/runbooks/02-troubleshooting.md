# Integration Service Troubleshooting Runbook

## Overview
This runbook provides step-by-step troubleshooting procedures for common issues with the Luminar Integration Service.

## Quick Diagnostics

### Service Health Check
```bash
# Overall health
curl https://api.luminar.com/api/integration/health

# Individual service health
curl https://api.luminar.com/api/integration/health/detailed
```

### Log Analysis
```bash
# Recent errors
kubectl logs -n production deployment/integration --tail=1000 | grep -E "ERROR|FATAL"

# Specific user issues
kubectl logs -n production deployment/integration | grep "user-123"

# WebSocket issues
kubectl logs -n production deployment/integration | grep "websocket"
```

## Common Issues and Solutions

### 1. High Response Times

#### Symptoms
- API response times > 500ms
- User complaints about slowness
- Grafana alerts for latency

#### Diagnosis
```bash
# Check current latency
curl -w "@curl-format.txt" -o /dev/null -s https://api.luminar.com/api/integration/health

# Check database query performance
psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check Redis latency
redis-cli --latency

# Check queue depth
curl https://api.luminar.com/api/integration/metrics | jq '.queues'
```

#### Solutions
1. **Database Optimization**
   ```bash
   # Run ANALYZE on tables
   psql -c "ANALYZE user_activities;"
   psql -c "ANALYZE user_integration_preferences;"
   
   # Check for missing indexes
   psql -f scripts/check-indexes.sql
   ```

2. **Cache Warming**
   ```bash
   # Warm up cache for active users
   ./scripts/warm-cache.sh --active-users
   ```

3. **Scale Services**
   ```bash
   # Increase replicas
   kubectl scale deployment/integration --replicas=5
   ```

### 2. WebSocket Connection Issues

#### Symptoms
- Real-time updates not working
- "Connection lost" errors
- WebSocket reconnection loops

#### Diagnosis
```bash
# Check WebSocket connections
ss -tan | grep :3000 | wc -l

# Check nginx WebSocket config
nginx -t

# Monitor WebSocket events
wscat -c wss://api.luminar.com/integration
```

#### Solutions
1. **Restart WebSocket Service**
   ```bash
   kubectl rollout restart deployment/integration-websocket
   ```

2. **Increase Connection Limits**
   ```bash
   # Update nginx config
   kubectl edit configmap nginx-config
   # Set: proxy_read_timeout 3600s;
   # Set: proxy_send_timeout 3600s;
   ```

3. **Clear Stuck Connections**
   ```bash
   redis-cli --scan --pattern "ws:connection:*" | xargs redis-cli del
   ```

### 3. Integration Sync Failures

#### Symptoms
- Data not syncing between applications
- Sync status shows failures
- Missing activities

#### Diagnosis
```bash
# Check sync status for user
curl https://api.luminar.com/api/integration/sync/user-123/status

# Check failed sync jobs
redis-cli lrange "bull:integration-sync:failed" 0 -1

# Check application connectors
curl https://api.luminar.com/api/integration/connectors/status
```

#### Solutions
1. **Retry Failed Syncs**
   ```bash
   # Retry specific user sync
   curl -X POST https://api.luminar.com/api/integration/sync/user-123/retry
   
   # Bulk retry failed syncs
   ./scripts/retry-failed-syncs.sh
   ```

2. **Reset Circuit Breaker**
   ```bash
   # Check circuit breaker status
   curl https://api.luminar.com/api/integration/circuit-breaker/status
   
   # Reset if needed
   curl -X POST https://api.luminar.com/api/integration/circuit-breaker/reset
   ```

3. **Fix Authentication Issues**
   ```bash
   # Refresh application tokens
   ./scripts/refresh-app-tokens.sh
   ```

### 4. Rate Limiting Issues

#### Symptoms
- 429 errors
- Users blocked from API
- Legitimate traffic being limited

#### Diagnosis
```bash
# Check rate limit status for user
redis-cli get "rate-limit:user:user-123:*"

# Check global rate limits
redis-cli --scan --pattern "global-rate-limit:*" | xargs redis-cli ttl

# Monitor rate limit hits
tail -f /var/log/nginx/access.log | grep " 429 "
```

#### Solutions
1. **Adjust Rate Limits**
   ```bash
   # Temporarily increase limits
   kubectl set env deployment/integration RATE_LIMIT_STANDARD=120
   ```

2. **Clear Rate Limit Keys**
   ```bash
   # Clear specific user limits
   redis-cli --scan --pattern "rate-limit:user:user-123:*" | xargs redis-cli del
   
   # Clear IP-based limits
   redis-cli --scan --pattern "rate-limit:ip:*" | xargs redis-cli del
   ```

3. **Whitelist Specific Users**
   ```bash
   # Add to whitelist
   redis-cli sadd "rate-limit:whitelist" "user-123"
   ```

### 5. Memory Issues

#### Symptoms
- Out of memory errors
- Service restarts
- Degraded performance

#### Diagnosis
```bash
# Check memory usage
kubectl top pods -n production | grep integration

# Check memory leaks
kubectl exec -it deployment/integration -- node --inspect-brk=0.0.0.0:9229

# Check heap dumps
kubectl exec deployment/integration -- kill -USR2 1
kubectl cp deployment/integration:/tmp/heapdump.123.heapsnapshot ./heapdump.heapsnapshot
```

#### Solutions
1. **Increase Memory Limits**
   ```bash
   kubectl set resources deployment/integration --limits=memory=2Gi
   ```

2. **Clear Caches**
   ```bash
   # Clear application cache
   redis-cli flushdb
   
   # Restart to clear memory
   kubectl rollout restart deployment/integration
   ```

3. **Optimize Code**
   - Review recent changes
   - Check for memory leaks
   - Implement streaming for large datasets

## Performance Tuning

### Database Optimization
```sql
-- Find slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE query LIKE '%integration%'
ORDER BY mean_time DESC 
LIMIT 20;

-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_activities_user_timestamp 
ON user_activities(user_id, timestamp);

-- Vacuum tables
VACUUM ANALYZE user_activities;
```

### Redis Optimization
```bash
# Check memory fragmentation
redis-cli info memory | grep fragmentation

# Optimize memory
redis-cli config set maxmemory-policy allkeys-lru

# Enable compression
redis-cli config set list-compress-depth 1
```

### Application Optimization
```bash
# Enable cluster mode
kubectl set env deployment/integration CLUSTER_ENABLED=true

# Adjust worker threads
kubectl set env deployment/integration WORKER_THREADS=4

# Enable aggressive caching
kubectl set env deployment/integration CACHE_TTL=3600
```

## Monitoring Commands

### Real-time Monitoring
```bash
# Watch error rate
watch -n 1 'curl -s https://api.luminar.com/api/integration/metrics | jq .errorRate'

# Monitor active connections
watch -n 1 'ss -tan | grep :3000 | wc -l'

# Track queue depth
watch -n 1 'redis-cli llen bull:integration-sync:wait'
```

### Trace Analysis
```bash
# Find slow traces
curl https://tracing.luminar.com/api/traces?service=integration&minDuration=1000

# Analyze specific trace
curl https://tracing.luminar.com/api/traces/<trace-id>
```

## Emergency Procedures

### Service Completely Down
1. Check pod status: `kubectl get pods -n production`
2. Check recent deployments: `kubectl rollout history deployment/integration`
3. Rollback if needed: `kubectl rollout undo deployment/integration`
4. Scale up replicas: `kubectl scale deployment/integration --replicas=10`

### Database Connection Issues
1. Check connection pool: `psql -c "SELECT count(*) FROM pg_stat_activity;"`
2. Kill idle connections: `psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'idle' AND state_change < NOW() - INTERVAL '10 minutes';"`
3. Restart connection pool: `kubectl rollout restart deployment/integration`

### Complete System Recovery
```bash
# 1. Stop all traffic
./scripts/enable-maintenance-mode.sh

# 2. Clear all caches
redis-cli flushall

# 3. Reset circuit breakers
curl -X POST https://api.luminar.com/api/integration/circuit-breaker/reset-all

# 4. Restart all services
kubectl rollout restart deployment/integration
kubectl rollout restart deployment/integration-websocket
kubectl rollout restart deployment/integration-worker

# 5. Warm up caches
./scripts/warm-all-caches.sh

# 6. Resume traffic
./scripts/disable-maintenance-mode.sh
```

## Useful Scripts

All scripts are located in `/opt/luminar/scripts/`:
- `check-health.sh` - Comprehensive health check
- `analyze-logs.sh` - Log analysis tool
- `performance-report.sh` - Generate performance report
- `debug-user.sh <user-id>` - Debug specific user issues
- `emergency-scale.sh` - Emergency scaling script