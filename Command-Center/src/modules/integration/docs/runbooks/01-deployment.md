# Integration Service Deployment Runbook

## Overview
This runbook covers the deployment process for the Luminar Integration Service across different environments.

## Prerequisites
- Access to deployment infrastructure
- Valid credentials for all integrated services
- <PERSON><PERSON> and <PERSON>er Compose installed
- Access to monitoring dashboards

## Deployment Steps

### 1. Pre-Deployment Checks

```bash
# Check current service health
curl https://api.luminar.com/api/integration/health

# Verify database connectivity
npm run typeorm:check

# Check Redis connectivity
redis-cli ping

# Verify all environment variables
npm run env:check
```

### 2. Database Migrations

```bash
# Run pending migrations
npm run typeorm:migration:run

# Verify migration status
npm run typeorm:migration:show

# If rollback needed
npm run typeorm:migration:revert
```

### 3. Build and Test

```bash
# Run tests
npm test
npm run test:integration

# Build application
npm run build

# Build Docker image
docker build -t luminar/integration:latest .
```

### 4. Deploy to Staging

```bash
# Update staging environment
cd deployment/staging
docker-compose down
docker-compose pull
docker-compose up -d

# Verify deployment
./scripts/verify-staging.sh
```

### 5. Smoke Tests

```bash
# Run smoke tests
npm run test:smoke -- --env=staging

# Check integration endpoints
curl https://staging-api.luminar.com/api/integration/health
```

### 6. Deploy to Production

```bash
# Blue-Green Deployment
cd deployment/production

# Deploy to blue environment
docker-compose -f docker-compose.blue.yml up -d

# Run health checks
./scripts/health-check-blue.sh

# Switch traffic to blue
./scripts/switch-to-blue.sh

# Verify production
curl https://api.luminar.com/api/integration/health
```

### 7. Post-Deployment Verification

```bash
# Check all integrations
./scripts/verify-integrations.sh

# Monitor error rates
./scripts/check-error-rates.sh

# Verify performance metrics
./scripts/check-performance.sh
```

## Rollback Procedure

### Immediate Rollback (< 5 minutes)
```bash
# Switch back to green environment
./scripts/switch-to-green.sh

# Verify rollback
curl https://api.luminar.com/api/integration/health
```

### Database Rollback
```bash
# Revert last migration
npm run typeorm:migration:revert

# Restore from backup if needed
./scripts/restore-database.sh --timestamp=<backup-timestamp>
```

## Monitoring Post-Deployment

### Key Metrics to Monitor
- Response time: < 200ms p95
- Error rate: < 0.1%
- WebSocket connections: Stable
- Queue depth: < 1000 messages

### Dashboard Links
- Grafana: https://monitoring.luminar.com/d/integration
- Jaeger: https://tracing.luminar.com
- Kibana: https://logs.luminar.com

## Common Issues

### Issue: High Error Rate
```bash
# Check recent errors
kubectl logs -n production deployment/integration --tail=100 | grep ERROR

# Check circuit breaker status
curl https://api.luminar.com/api/integration/circuit-breaker/status
```

### Issue: Database Connection Pool Exhausted
```bash
# Increase pool size temporarily
kubectl set env deployment/integration DB_POOL_SIZE=50

# Monitor connections
psql -c "SELECT count(*) FROM pg_stat_activity WHERE datname='luminar';"
```

### Issue: Redis Memory Full
```bash
# Check memory usage
redis-cli info memory

# Clear expired keys
redis-cli --scan --pattern "rate-limit:*" | xargs redis-cli del
```

## Emergency Contacts
- On-Call Engineer: +1-XXX-XXX-XXXX
- Platform Team: <EMAIL>
- Database Admin: <EMAIL>

## Deployment Checklist
- [ ] Pre-deployment health check passed
- [ ] Database migrations completed
- [ ] All tests passed
- [ ] Docker image built and pushed
- [ ] Staging deployment verified
- [ ] Smoke tests passed
- [ ] Production deployment completed
- [ ] Post-deployment verification done
- [ ] Monitoring alerts configured
- [ ] Rollback plan tested