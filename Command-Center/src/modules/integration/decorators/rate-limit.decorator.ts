import { SetMetadata } from '@nestjs/common';
import { RateLimitOptions, RATE_LIMIT_KEY } from '../guards/enhanced-rate-limit.guard';

/**
 * Rate limit decorator for methods and classes
 */
export const RateLimit = (options: RateLimitOptions) => 
  SetMetadata(RATE_LIMIT_KEY, options);

/**
 * Common rate limit configurations
 */
export const RateLimitPresets = {
  /**
   * Strict rate limiting for sensitive operations
   */
  strict: (): RateLimitOptions => ({
    windowMs: 60000, // 1 minute
    maxRequests: 10,
    enableExponentialBackoff: true,
    backoffMultiplier: 3,
    maxBackoffMs: 900000, // 15 minutes
  }),

  /**
   * Standard rate limiting for regular API endpoints
   */
  standard: (): RateLimitOptions => ({
    windowMs: 60000, // 1 minute
    maxRequests: 60,
    enableExponentialBackoff: true,
    backoffMultiplier: 2,
    maxBackoffMs: 300000, // 5 minutes
  }),

  /**
   * Relaxed rate limiting for read operations
   */
  relaxed: (): RateLimitOptions => ({
    windowMs: 60000, // 1 minute
    maxRequests: 200,
    enableExponentialBackoff: false,
  }),

  /**
   * Per-user rate limiting
   */
  perUser: (maxRequests: number = 100): RateLimitOptions => ({
    windowMs: 60000,
    maxRequests,
    keyGenerator: (request) => `user:${request.user?.id || 'anonymous'}`,
  }),

  /**
   * Per-application rate limiting for integrations
   */
  perApplication: (maxRequests: number = 500): RateLimitOptions => ({
    windowMs: 60000,
    maxRequests,
    keyGenerator: (request) => {
      const source = request.body?.source || request.headers['x-source-application'] || 'unknown';
      return `app:${source}`;
    },
  }),

  /**
   * Burst rate limiting (allows bursts but limits sustained traffic)
   */
  burst: (burstSize: number = 50, sustainedRate: number = 10): RateLimitOptions => ({
    windowMs: 10000, // 10 seconds
    maxRequests: burstSize,
    enableExponentialBackoff: true,
    onLimitReached: async (request) => {
      // Log burst limit reached for monitoring
      console.warn(`Burst limit reached for ${request.user?.id || request.ip}`);
    },
  }),

  /**
   * WebSocket rate limiting
   */
  websocket: (): RateLimitOptions => ({
    windowMs: 1000, // 1 second
    maxRequests: 20, // 20 messages per second
    keyGenerator: (request) => `ws:${request.socketId || request.user?.id}`,
    skipSuccessfulRequests: false,
  }),

  /**
   * File upload rate limiting
   */
  fileUpload: (): RateLimitOptions => ({
    windowMs: 300000, // 5 minutes
    maxRequests: 10, // 10 uploads per 5 minutes
    keyGenerator: (request) => `upload:${request.user?.id}:${request.ip}`,
    onLimitReached: (request) => {
      console.warn(`Upload limit reached for user ${request.user?.id}`);
    },
  }),
};

/**
 * Skip rate limiting for specific conditions
 */
export const SkipRateLimit = () => SetMetadata(RATE_LIMIT_KEY, { skip: true });

/**
 * Apply multiple rate limits
 */
export const MultiRateLimit = (...limits: RateLimitOptions[]) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    limits.forEach((limit, index) => {
      const metadataKey = `${RATE_LIMIT_KEY}:${index}`;
      SetMetadata(metadataKey, limit)(target, propertyKey, descriptor);
    });
  };
};