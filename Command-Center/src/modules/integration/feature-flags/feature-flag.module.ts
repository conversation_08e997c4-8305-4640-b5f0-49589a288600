import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { FeatureFlagService } from './feature-flag.service';
import { FeatureFlagController } from './feature-flag.controller';
import { LaunchDarklyProvider } from './providers/launchdarkly.provider';
import { LocalProvider } from './providers/local.provider';
import { RedisProvider } from './providers/redis.provider';

@Global()
@Module({
  imports: [ConfigModule, HttpModule],
  controllers: [FeatureFlagController],
  providers: [
    FeatureFlagService,
    {
      provide: 'FEATURE_FLAG_PROVIDER',
      useFactory: (configService) => {
        const provider = configService.get('FEATURE_FLAG_PROVIDER', 'local');
        
        switch (provider) {
          case 'launchdarkly':
            return new LaunchDarklyProvider(configService);
          case 'redis':
            return new RedisProvider(configService);
          default:
            return new LocalProvider(configService);
        }
      },
      inject: ['ConfigService'],
    },
  ],
  exports: [FeatureFlagService],
})
export class FeatureFlagModule {}