import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { FeatureFlagUser } from '../interfaces/feature-flag.interface';

/**
 * Parameter decorator to inject feature flag value
 */
export const FeatureFlag = createParamDecorator(
  async (flagKey: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const featureFlagService = request.app.get('FeatureFlagService');
    
    const user: FeatureFlagUser = request.user ? {
      id: request.user.id,
      email: request.user.email,
      roles: request.user.roles,
      attributes: request.user.attributes,
    } : undefined;

    return featureFlagService.isEnabled(flagKey, user);
  },
);

/**
 * Method decorator to conditionally execute based on feature flag
 */
export const RequireFeatureFlag = (flagKey: string, defaultValue: boolean = false) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const featureFlagService = (this as any).featureFlagService;
      
      if (!featureFlagService) {
        throw new Error('FeatureFlagService not found. Ensure it is injected in the class.');
      }

      // Extract user from request if available
      let user: FeatureFlagUser | undefined;
      const request = args.find(arg => arg?.user && arg?.headers);
      
      if (request?.user) {
        user = {
          id: request.user.id,
          email: request.user.email,
          roles: request.user.roles,
          attributes: request.user.attributes,
        };
      }

      const isEnabled = await featureFlagService.isEnabled(flagKey, user, defaultValue);
      
      if (!isEnabled) {
        throw new Error(`Feature flag ${flagKey} is not enabled`);
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
};

/**
 * Class decorator to add feature flag metadata
 */
export const FeatureFlagClass = (flags: Record<string, boolean>) => {
  return (target: any) => {
    SetMetadata('feature-flags', flags)(target);
    return target;
  };
};

/**
 * Conditional feature flag guard
 */
export const IfFeatureEnabled = (flagKey: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const featureFlagService = (this as any).featureFlagService;
      
      if (!featureFlagService) {
        return null; // Silently skip if service not available
      }

      const isEnabled = await featureFlagService.isEnabled(flagKey);
      
      if (isEnabled) {
        return originalMethod.apply(this, args);
      }

      return null; // Skip execution if flag is disabled
    };

    return descriptor;
  };
};

/**
 * Feature flag value decorator
 */
export const FeatureFlagValue = createParamDecorator(
  async (data: { key: string; defaultValue?: any }, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const featureFlagService = request.app.get('FeatureFlagService');
    
    const user: FeatureFlagUser = request.user ? {
      id: request.user.id,
      email: request.user.email,
      roles: request.user.roles,
      attributes: request.user.attributes,
    } : undefined;

    return featureFlagService.getValue(data.key, user, data.defaultValue);
  },
);