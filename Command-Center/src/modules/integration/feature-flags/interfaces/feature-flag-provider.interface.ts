import { 
  FeatureFlag, 
  FeatureFlagUser, 
  FeatureFlagValue, 
  FeatureFlagEvaluation,
  FeatureFlagConfig,
} from './feature-flag.interface';

export interface FeatureFlagProvider {
  /**
   * Initialize the provider
   */
  initialize(): Promise<void>;

  /**
   * Evaluate a feature flag
   */
  evaluate(
    flagKey: string,
    user?: FeatureFlagUser,
    defaultValue?: FeatureFlagValue,
  ): Promise<FeatureFlagEvaluation>;

  /**
   * Get all flags for a user
   */
  getAllFlags(user?: FeatureFlagUser): Promise<Record<string, FeatureFlagValue>>;

  /**
   * Update a feature flag (optional)
   */
  updateFlag?(flagKey: string, config: FeatureFlagConfig): Promise<void>;

  /**
   * Create a feature flag (optional)
   */
  createFlag?(flag: FeatureFlag): Promise<void>;

  /**
   * Delete a feature flag (optional)
   */
  deleteFlag?(flagKey: string): Promise<void>;

  /**
   * Start polling for updates (optional)
   */
  startPolling?(callback: (flags: Record<string, FeatureFlagValue>) => void): void;

  /**
   * Close the provider (optional)
   */
  close?(): Promise<void>;
}