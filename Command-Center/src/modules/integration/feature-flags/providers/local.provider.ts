import { ConfigService } from '@nestjs/config';
import { FeatureFlagProvider } from '../interfaces/feature-flag-provider.interface';
import {
  FeatureFlag,
  FeatureFlagUser,
  FeatureFlagValue,
  FeatureFlagEvaluation,
  FeatureFlagConfig,
} from '../interfaces/feature-flag.interface';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Local file-based feature flag provider for development
 */
export class LocalProvider implements FeatureFlagProvider {
  private flags: Map<string, FeatureFlag> = new Map();
  private configPath: string;
  private watchInterval: NodeJS.Timer | null = null;

  constructor(private readonly configService: ConfigService) {
    this.configPath = this.configService.get(
      'FEATURE_FLAGS_CONFIG_PATH',
      path.join(process.cwd(), 'feature-flags.json'),
    );
  }

  async initialize(): Promise<void> {
    await this.loadFlags();
  }

  async evaluate(
    flagKey: string,
    user?: FeatureFlagUser,
    defaultValue?: FeatureFlagValue,
  ): Promise<FeatureFlagEvaluation> {
    const flag = this.flags.get(flagKey);

    if (!flag || !flag.enabled) {
      return {
        value: defaultValue ?? false,
        reason: { kind: 'OFF' },
        timestamp: Date.now(),
      };
    }

    // Check targets
    if (flag.targets && user) {
      for (const target of flag.targets) {
        if (target.users?.includes(user.id)) {
          const variation = flag.variations?.find(v => v.id === target.variation);
          return {
            value: variation?.value ?? flag.defaultValue,
            variation: target.variation,
            reason: { kind: 'TARGET_MATCH' },
            timestamp: Date.now(),
          };
        }
      }
    }

    // Check rules
    if (flag.rules && user) {
      for (let i = 0; i < flag.rules.length; i++) {
        const rule = flag.rules[i];
        if (this.evaluateRule(rule, user)) {
          const variation = flag.variations?.find(v => v.id === rule.variation);
          return {
            value: variation?.value ?? flag.defaultValue,
            variation: rule.variation,
            reason: { kind: 'RULE_MATCH', ruleIndex: i, ruleId: rule.id },
            timestamp: Date.now(),
          };
        }
      }
    }

    // Rollout
    if (flag.rollout && user) {
      const bucket = this.getBucket(user.id, flagKey);
      let accumulated = 0;

      for (const variant of flag.rollout.variations) {
        accumulated += variant.weight;
        if (bucket < accumulated) {
          const variation = flag.variations?.find(v => v.id === variant.variation);
          return {
            value: variation?.value ?? flag.defaultValue,
            variation: variant.variation,
            reason: { kind: 'FALLTHROUGH' },
            timestamp: Date.now(),
          };
        }
      }
    }

    // Default value
    return {
      value: flag.defaultValue,
      reason: { kind: 'FALLTHROUGH' },
      timestamp: Date.now(),
    };
  }

  async getAllFlags(user?: FeatureFlagUser): Promise<Record<string, FeatureFlagValue>> {
    const result: Record<string, FeatureFlagValue> = {};

    for (const [key, flag] of this.flags) {
      const evaluation = await this.evaluate(key, user, flag.defaultValue);
      result[key] = evaluation.value;
    }

    return result;
  }

  async updateFlag(flagKey: string, config: FeatureFlagConfig): Promise<void> {
    const flag = this.flags.get(flagKey);
    if (!flag) {
      throw new Error(`Flag ${flagKey} not found`);
    }

    // Update flag properties
    Object.assign(flag, config, { updatedAt: new Date() });
    
    await this.saveFlags();
  }

  async createFlag(flag: FeatureFlag): Promise<void> {
    if (this.flags.has(flag.key)) {
      throw new Error(`Flag ${flag.key} already exists`);
    }

    this.flags.set(flag.key, {
      ...flag,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await this.saveFlags();
  }

  async deleteFlag(flagKey: string): Promise<void> {
    if (!this.flags.delete(flagKey)) {
      throw new Error(`Flag ${flagKey} not found`);
    }

    await this.saveFlags();
  }

  startPolling(callback: (flags: Record<string, FeatureFlagValue>) => void): void {
    // Watch for file changes
    this.watchInterval = setInterval(async () => {
      try {
        const oldFlags = new Map(this.flags);
        await this.loadFlags();

        // Check for changes
        const changed: Record<string, FeatureFlagValue> = {};
        
        for (const [key, flag] of this.flags) {
          const oldFlag = oldFlags.get(key);
          if (!oldFlag || JSON.stringify(oldFlag) !== JSON.stringify(flag)) {
            changed[key] = flag.defaultValue;
          }
        }

        if (Object.keys(changed).length > 0) {
          callback(changed);
        }
      } catch (error) {
        console.error('Error polling feature flags:', error);
      }
    }, 5000); // Poll every 5 seconds
  }

  async close(): Promise<void> {
    if (this.watchInterval) {
      clearInterval(this.watchInterval);
      this.watchInterval = null;
    }
  }

  private async loadFlags(): Promise<void> {
    try {
      const data = await fs.readFile(this.configPath, 'utf-8');
      const flagsArray: FeatureFlag[] = JSON.parse(data);
      
      this.flags.clear();
      flagsArray.forEach(flag => {
        this.flags.set(flag.key, flag);
      });
    } catch (error) {
      // If file doesn't exist, create default flags
      if ((error as any).code === 'ENOENT') {
        await this.createDefaultFlags();
      } else {
        throw error;
      }
    }
  }

  private async saveFlags(): Promise<void> {
    const flagsArray = Array.from(this.flags.values());
    await fs.writeFile(this.configPath, JSON.stringify(flagsArray, null, 2));
  }

  private async createDefaultFlags(): Promise<void> {
    const defaultFlags: FeatureFlag[] = [
      {
        key: 'integration.e-connect.enabled',
        name: 'E-Connect Integration',
        description: 'Enable E-Connect email integration',
        defaultValue: true,
        enabled: true,
      },
      {
        key: 'integration.lighthouse.enabled',
        name: 'Lighthouse Integration',
        description: 'Enable Lighthouse research integration',
        defaultValue: true,
        enabled: true,
      },
      {
        key: 'integration.training.enabled',
        name: 'Training Integration',
        description: 'Enable Training integration',
        defaultValue: true,
        enabled: true,
      },
      {
        key: 'integration.vendors.enabled',
        name: 'Vendors Integration',
        description: 'Enable Vendors integration',
        defaultValue: true,
        enabled: true,
      },
      {
        key: 'integration.wins.enabled',
        name: 'Wins Integration',
        description: 'Enable Wins-of-Week integration',
        defaultValue: true,
        enabled: true,
      },
      {
        key: 'integration.realtime-sync.enabled',
        name: 'Real-time Sync',
        description: 'Enable real-time data synchronization',
        defaultValue: false,
        enabled: true,
        rollout: {
          variations: [
            { variation: 'false', weight: 50 },
            { variation: 'true', weight: 50 },
          ],
        },
      },
      {
        key: 'integration.ai-insights.enabled',
        name: 'AI Insights',
        description: 'Enable AI-powered insights',
        defaultValue: false,
        enabled: true,
        rollout: {
          variations: [
            { variation: 'false', weight: 80 },
            { variation: 'true', weight: 20 },
          ],
        },
      },
    ];

    defaultFlags.forEach(flag => {
      this.flags.set(flag.key, flag);
    });

    await this.saveFlags();
  }

  private evaluateRule(rule: any, user: FeatureFlagUser): boolean {
    for (const clause of rule.clauses) {
      if (!this.evaluateClause(clause, user)) {
        return false;
      }
    }
    return true;
  }

  private evaluateClause(clause: any, user: FeatureFlagUser): boolean {
    const userValue = this.getUserAttribute(user, clause.attribute);
    let result = false;

    switch (clause.operator) {
      case 'in':
        result = clause.values.includes(userValue);
        break;
      case 'notIn':
        result = !clause.values.includes(userValue);
        break;
      case 'equals':
        result = userValue === clause.values[0];
        break;
      case 'notEquals':
        result = userValue !== clause.values[0];
        break;
      case 'contains':
        result = String(userValue).includes(clause.values[0]);
        break;
      case 'startsWith':
        result = String(userValue).startsWith(clause.values[0]);
        break;
      case 'endsWith':
        result = String(userValue).endsWith(clause.values[0]);
        break;
      default:
        result = false;
    }

    return clause.negate ? !result : result;
  }

  private getUserAttribute(user: FeatureFlagUser, attribute: string): any {
    const parts = attribute.split('.');
    let value: any = user;

    for (const part of parts) {
      value = value?.[part];
      if (value === undefined) break;
    }

    return value;
  }

  private getBucket(userId: string, flagKey: string): number {
    // Simple hash function for bucketing
    const str = `${userId}.${flagKey}`;
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash) % 100;
  }
}