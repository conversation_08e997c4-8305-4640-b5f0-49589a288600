import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BullModule } from '@nestjs/bull';

// Controllers
import { IntegrationController } from './controllers/integration.controller';
import { PreferencesController } from './controllers/preferences.controller';
import { ActivityController } from './controllers/activity.controller';
import { HealthController } from './controllers/health.controller';

// Core Services
import { AMNAIntegrationService } from './services/core/amna-integration.service';
import { DataFederationService } from './services/core/data-federation.service';
import { EventBusService } from './services/core/event-bus.service';
import { UserPreferencesService } from './services/core/user-preferences.service';
import { ActivityTrackerService } from './services/core/activity-tracker.service';
import { CircuitBreakerService } from './services/core/circuit-breaker.service';
import { CacheManagerService } from './services/core/cache-manager.service';

// Connector Services
import { EConnectConnectorService } from './services/connectors/e-connect-connector.service';
import { LighthouseConnectorService } from './services/connectors/lighthouse-connector.service';
import { TrainingConnectorService } from './services/connectors/training-connector.service';
import { VendorsConnectorService } from './services/connectors/vendors-connector.service';
import { WinsConnectorService } from './services/connectors/wins-connector.service';

// Adapter Services
import { EConnectAdapterService } from './services/adapters/e-connect-adapter.service';
import { LighthouseAdapterService } from './services/adapters/lighthouse-adapter.service';
import { TrainingAdapterService } from './services/adapters/training-adapter.service';
import { VendorsAdapterService } from './services/adapters/vendors-adapter.service';

// Monitoring Services
import { HealthCheckService } from './services/monitoring/health-check.service';
import { MetricsService } from './services/monitoring/metrics.service';
import { PerformanceMonitorService } from './services/monitoring/performance-monitor.service';
import { ErrorTrackerService } from './services/monitoring/error-tracker.service';

// Guards
import { IntegrationGuard } from './guards/integration.guard';
import { EnhancedRateLimitGuard, CircuitBreakerRateLimitGuard } from './guards/enhanced-rate-limit.guard';
import { PermissionsGuard } from './guards/permissions.guard';

// Entities
import { UserIntegrationPreferences } from './entities/user-integration-preferences.entity';
import { UserActivities } from './entities/user-activities.entity';
import { IntegrationLogs } from './entities/integration-logs.entity';
import { SyncStatus } from './entities/sync-status.entity';

// AMNA Module
import { AMNAModule } from '../amna/amna.module';

// Tracing
import { TracingModule } from './tracing/tracing.module';
import { IntegrationTracingService } from './tracing/integration-tracing.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    TypeOrmModule.forFeature([
      UserIntegrationPreferences,
      UserActivities,
      IntegrationLogs,
      SyncStatus,
    ]),
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
      verboseMemoryLeak: true,
      ignoreErrors: false,
    }),
    BullModule.registerQueue(
      { name: 'integration-sync' },
      { name: 'activity-processing' },
      { name: 'notification-queue' },
    ),
    AMNAModule,
    TracingModule,
  ],
  controllers: [
    IntegrationController,
    PreferencesController,
    ActivityController,
    HealthController,
  ],
  providers: [
    // Core Services
    AMNAIntegrationService,
    DataFederationService,
    EventBusService,
    UserPreferencesService,
    ActivityTrackerService,
    CircuitBreakerService,
    CacheManagerService,

    // Connector Services
    EConnectConnectorService,
    LighthouseConnectorService,
    TrainingConnectorService,
    VendorsConnectorService,
    WinsConnectorService,

    // Adapter Services
    EConnectAdapterService,
    LighthouseAdapterService,
    TrainingAdapterService,
    VendorsAdapterService,

    // Monitoring Services
    HealthCheckService,
    MetricsService,
    PerformanceMonitorService,
    ErrorTrackerService,

    // Guards
    IntegrationGuard,
    EnhancedRateLimitGuard,
    CircuitBreakerRateLimitGuard,
    PermissionsGuard,

    // Tracing
    IntegrationTracingService,
  ],
  exports: [
    AMNAIntegrationService,
    EventBusService,
    UserPreferencesService,
    ActivityTrackerService,
    CacheManagerService,
    DataFederationService,
    IntegrationTracingService,
  ],
})
export class IntegrationModule {}
