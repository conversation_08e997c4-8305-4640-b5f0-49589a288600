import { Injectable, Inject, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { 
  trace, 
  context, 
  SpanKind, 
  SpanStatusCode,
  Tracer,
  Span,
  SpanOptions,
  Context,
  propagation,
} from '@opentelemetry/api';
import { W3CTraceContextPropagator } from '@opentelemetry/core';
import { Logger } from '@nestjs/common';

interface TracingConfig {
  serviceName: string;
  endpoint: string;
  headers: string;
  environment: string;
  enableTracing: boolean;
  samplingRate: number;
}

@Injectable()
export class TracingService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(TracingService.name);
  private sdk: NodeSDK;
  private tracer: Tracer;

  constructor(
    @Inject('TRACING_CONFIG') private readonly config: TracingConfig,
  ) {}

  async onModuleInit() {
    if (!this.config.enableTracing) {
      this.logger.log('Tracing is disabled');
      return;
    }

    try {
      // Initialize OpenTelemetry
      const resource = Resource.default().merge(
        new Resource({
          [SemanticResourceAttributes.SERVICE_NAME]: this.config.serviceName,
          [SemanticResourceAttributes.SERVICE_VERSION]: process.env.npm_package_version || '1.0.0',
          [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: this.config.environment,
        }),
      );

      const traceExporter = new OTLPTraceExporter({
        url: this.config.endpoint,
        headers: this.parseHeaders(this.config.headers),
      });

      this.sdk = new NodeSDK({
        resource,
        traceExporter,
        spanProcessor: new BatchSpanProcessor(traceExporter),
        instrumentations: [
          getNodeAutoInstrumentations({
            '@opentelemetry/instrumentation-fs': {
              enabled: false, // Disable fs instrumentation to reduce noise
            },
            '@opentelemetry/instrumentation-http': {
              requestHook: (span, request) => {
                span.setAttributes({
                  'http.request.body.size': request.headers['content-length'] || 0,
                  'http.user_agent': request.headers['user-agent'] || 'unknown',
                });
              },
            },
            '@opentelemetry/instrumentation-nestjs-core': {
              enabled: true,
            },
          }),
        ],
      });

      await this.sdk.start();
      
      // Set global propagator
      propagation.setGlobalPropagator(new W3CTraceContextPropagator());
      
      this.tracer = trace.getTracer(
        this.config.serviceName,
        process.env.npm_package_version || '1.0.0',
      );

      this.logger.log('OpenTelemetry tracing initialized');
    } catch (error) {
      this.logger.error('Failed to initialize OpenTelemetry', error);
    }
  }

  async onModuleDestroy() {
    if (this.sdk) {
      try {
        await this.sdk.shutdown();
        this.logger.log('OpenTelemetry SDK shut down');
      } catch (error) {
        this.logger.error('Error shutting down OpenTelemetry SDK', error);
      }
    }
  }

  /**
   * Start a new span
   */
  startSpan(name: string, options?: SpanOptions): Span {
    if (!this.tracer) {
      return trace.getTracer('default').startSpan(name, options);
    }
    return this.tracer.startSpan(name, options);
  }

  /**
   * Start a span and execute a function within its context
   */
  async withSpan<T>(
    name: string,
    fn: (span: Span) => Promise<T>,
    options?: SpanOptions,
  ): Promise<T> {
    const span = this.startSpan(name, {
      ...options,
      kind: options?.kind || SpanKind.INTERNAL,
    });

    try {
      const result = await context.with(
        trace.setSpan(context.active(), span),
        () => fn(span),
      );
      span.setStatus({ code: SpanStatusCode.OK });
      return result;
    } catch (error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      span.recordException(error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Add attributes to the current span
   */
  addAttributes(attributes: Record<string, any>) {
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes(attributes);
    }
  }

  /**
   * Add an event to the current span
   */
  addEvent(name: string, attributes?: Record<string, any>) {
    const span = trace.getActiveSpan();
    if (span) {
      span.addEvent(name, attributes);
    }
  }

  /**
   * Get the current trace ID
   */
  getCurrentTraceId(): string {
    const span = trace.getActiveSpan();
    if (span) {
      const spanContext = span.spanContext();
      return spanContext.traceId;
    }
    return '';
  }

  /**
   * Extract trace context from headers
   */
  extractContext(headers: Record<string, string>): Context {
    return propagation.extract(context.active(), headers);
  }

  /**
   * Inject trace context into headers
   */
  injectContext(headers: Record<string, string>): Record<string, string> {
    const newHeaders = { ...headers };
    propagation.inject(context.active(), newHeaders);
    return newHeaders;
  }

  /**
   * Create a span for cross-application communication
   */
  createCrossAppSpan(
    applicationName: string,
    operationType: string,
    attributes?: Record<string, any>,
  ): Span {
    return this.startSpan(`${applicationName}.${operationType}`, {
      kind: SpanKind.CLIENT,
      attributes: {
        'integration.application': applicationName,
        'integration.operation': operationType,
        ...attributes,
      },
    });
  }

  /**
   * Record integration metrics
   */
  recordIntegrationMetrics(
    source: string,
    destination: string,
    success: boolean,
    duration: number,
    attributes?: Record<string, any>,
  ) {
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'integration.source': source,
        'integration.destination': destination,
        'integration.success': success,
        'integration.duration_ms': duration,
        ...attributes,
      });

      span.addEvent(success ? 'integration.success' : 'integration.failure', {
        source,
        destination,
        duration,
      });
    }
  }

  private parseHeaders(headerString: string): Record<string, string> {
    if (!headerString) return {};
    
    const headers: Record<string, string> = {};
    headerString.split(',').forEach(header => {
      const [key, value] = header.split('=');
      if (key && value) {
        headers[key.trim()] = value.trim();
      }
    });
    
    return headers;
  }
}