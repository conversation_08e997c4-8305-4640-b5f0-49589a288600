import { SetMetadata } from '@nestjs/common';
import { SpanOptions } from '@opentelemetry/api';

export const TRACE_SPAN_KEY = 'trace:span';

export interface TraceOptions extends SpanOptions {
  name?: string;
  recordArgs?: boolean;
  recordResult?: boolean;
}

/**
 * Decorator to add tracing to a method
 */
export const Trace = (options?: TraceOptions): MethodDecorator => {
  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    const methodName = String(propertyKey);
    const className = target.constructor.name;

    descriptor.value = async function (...args: any[]) {
      const tracingService = (this as any).tracingService;
      
      if (!tracingService) {
        // If no tracing service is available, just execute the method
        return originalMethod.apply(this, args);
      }

      const spanName = options?.name || `${className}.${methodName}`;
      
      return tracingService.withSpan(
        spanName,
        async (span) => {
          // Record method arguments if requested
          if (options?.recordArgs) {
            span.setAttributes({
              'method.args': JSON.stringify(args),
            });
          }

          try {
            const result = await originalMethod.apply(this, args);
            
            // Record method result if requested
            if (options?.recordResult) {
              span.setAttributes({
                'method.result': JSON.stringify(result),
              });
            }

            return result;
          } catch (error) {
            span.recordException(error);
            throw error;
          }
        },
        options,
      );
    };

    return descriptor;
  };
};

/**
 * Decorator to mark a class for tracing
 */
export const TraceClass = (): ClassDecorator => {
  return (target: any) => {
    SetMetadata(TRACE_SPAN_KEY, true)(target);
    return target;
  };
};