import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>xt,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { TracingService } from './tracing.service';
import { SpanKind, SpanStatusCode } from '@opentelemetry/api';

@Injectable()
export class TracingInterceptor implements NestInterceptor {
  constructor(private readonly tracingService: TracingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const handler = context.getHandler();
    const controller = context.getClass();
    
    const spanName = `${controller.name}.${handler.name}`;
    const startTime = Date.now();

    // Extract trace context from incoming request
    const incomingContext = this.tracingService.extractContext(request.headers);

    return this.tracingService.withSpan(
      spanName,
      async (span) => {
        // Add request attributes
        span.setAttributes({
          'http.method': request.method,
          'http.url': request.url,
          'http.target': request.path,
          'http.host': request.headers.host,
          'http.scheme': request.protocol,
          'http.user_agent': request.headers['user-agent'] || 'unknown',
          'integration.controller': controller.name,
          'integration.handler': handler.name,
          'user.id': request.user?.id || 'anonymous',
        });

        // Add custom integration attributes
        if (request.body?.source) {
          span.setAttribute('integration.source', request.body.source);
        }
        if (request.body?.type) {
          span.setAttribute('integration.event_type', request.body.type);
        }

        return new Promise((resolve, reject) => {
          next.handle()
            .pipe(
              tap({
                next: (data) => {
                  const duration = Date.now() - startTime;
                  
                  // Add response attributes
                  span.setAttributes({
                    'http.status_code': response.statusCode,
                    'http.response.size': JSON.stringify(data).length,
                    'integration.duration_ms': duration,
                  });

                  // Record successful integration
                  if (request.body?.source && request.body?.destination) {
                    this.tracingService.recordIntegrationMetrics(
                      request.body.source,
                      request.body.destination,
                      true,
                      duration,
                    );
                  }

                  span.setStatus({ code: SpanStatusCode.OK });
                  resolve(data);
                },
                error: (error) => {
                  const duration = Date.now() - startTime;
                  
                  span.setAttributes({
                    'http.status_code': error.status || 500,
                    'error': true,
                    'error.message': error.message,
                    'error.stack': error.stack,
                    'integration.duration_ms': duration,
                  });

                  // Record failed integration
                  if (request.body?.source && request.body?.destination) {
                    this.tracingService.recordIntegrationMetrics(
                      request.body.source,
                      request.body.destination,
                      false,
                      duration,
                      { error: error.message },
                    );
                  }

                  span.setStatus({
                    code: SpanStatusCode.ERROR,
                    message: error.message,
                  });
                  span.recordException(error);
                  
                  reject(error);
                },
              }),
            )
            .subscribe();
        });
      },
      {
        kind: SpanKind.SERVER,
        attributes: {
          'component': 'nestjs',
          'integration.interceptor': 'tracing',
        },
      },
    );
  }
}