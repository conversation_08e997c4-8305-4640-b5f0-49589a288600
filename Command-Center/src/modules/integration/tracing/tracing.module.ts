import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TracingService } from './tracing.service';
import { TracingInterceptor } from './tracing.interceptor';
import { TracingDecorator } from './tracing.decorator';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    TracingService,
    TracingInterceptor,
    {
      provide: 'TRACING_CONFIG',
      useFactory: (configService: ConfigService) => ({
        serviceName: configService.get('OTEL_SERVICE_NAME', 'integration-service'),
        endpoint: configService.get('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),
        headers: configService.get('OTEL_EXPORTER_OTLP_HEADERS', ''),
        environment: configService.get('NODE_ENV', 'development'),
        enableTracing: configService.get('ENABLE_TRACING', 'true') === 'true',
        samplingRate: parseFloat(configService.get('OTEL_SAMPLING_RATE', '1.0')),
      }),
      inject: [ConfigService],
    },
  ],
  exports: [TracingService, TracingInterceptor],
})
export class TracingModule {}