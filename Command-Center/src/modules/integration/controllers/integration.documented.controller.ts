import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiHeader,
  ApiSecurity,
  ApiExtraModels,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import {
  CreateActivityDto,
  UpdatePreferencesDto,
  SyncRequestDto,
  QueryActivitiesDto,
  ActivityResponseDto,
  PreferencesResponseDto,
  SyncStatusResponseDto,
  FederatedDataResponseDto,
  HealthStatusResponseDto,
  ErrorResponseDto,
  RateLimitErrorDto,
} from '../dto/api-docs.dto';

/**
 * Integration API Controller with full OpenAPI documentation
 * 
 * This controller manages cross-application integrations for the Luminar ecosystem,
 * including AMNA (AI assistant), E-Connect (email), Lighthouse (research), 
 * Training, Vendors, and Wins-of-Week applications.
 */
@ApiTags('Integration')
@Controller('api/integration')
@ApiBearerAuth()
@ApiSecurity('api_key')
@ApiExtraModels(
  ErrorResponseDto,
  RateLimitErrorDto,
  ActivityResponseDto,
  PreferencesResponseDto,
  SyncStatusResponseDto,
  FederatedDataResponseDto,
  HealthStatusResponseDto,
)
export class IntegrationDocumentedController {

  /**
   * Create a new integration activity
   */
  @Post('activity')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Create integration activity',
    description: `Records a new activity from any integrated application. Activities are processed
    asynchronously and may trigger cross-application workflows. AMNA analyzes activities
    to provide insights and recommendations.`,
  })
  @ApiBody({
    type: CreateActivityDto,
    description: 'Activity data to be recorded',
    examples: {
      emailActivity: {
        summary: 'Email sent activity',
        value: {
          userId: 'user-123',
          source: 'e-connect',
          type: 'email.sent',
          data: {
            to: '<EMAIL>',
            subject: 'Project Update',
            sentiment: 'positive',
          },
        },
      },
      trainingActivity: {
        summary: 'Skill gap identified',
        value: {
          userId: 'user-123',
          source: 'training',
          type: 'skill.gap.identified',
          data: {
            skill: 'Cloud Architecture',
            currentLevel: 2,
            requiredLevel: 4,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Activity created successfully',
    type: ActivityResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid activity data',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.TOO_MANY_REQUESTS,
    description: 'Rate limit exceeded',
    type: RateLimitErrorDto,
  })
  async createActivity(@Body() createActivityDto: CreateActivityDto): Promise<ActivityResponseDto> {
    // Implementation
    return {} as ActivityResponseDto;
  }

  /**
   * Query activities with filters
   */
  @Get('activities/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Get user activities',
    description: `Retrieves activities for a specific user with optional filtering by source,
    type, and date range. Results are paginated and sorted by timestamp.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to fetch activities for',
    example: 'user-123',
  })
  @ApiQuery({
    name: 'source',
    required: false,
    enum: ['e-connect', 'lighthouse', 'training', 'vendors', 'wins-of-week', 'amna'],
    description: 'Filter by source application',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by activity type',
    example: 'email.sent',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for filtering (ISO 8601)',
    example: '2024-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for filtering (ISO 8601)',
    example: '2024-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of results to return',
    example: 50,
    minimum: 1,
    maximum: 1000,
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Offset for pagination',
    example: 0,
    minimum: 0,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        activities: {
          type: 'array',
          items: { $ref: '#/components/schemas/ActivityResponseDto' },
        },
        total: { type: 'number', example: 150 },
        limit: { type: 'number', example: 50 },
        offset: { type: 'number', example: 0 },
      },
    },
  })
  async getActivities(
    @Param('userId') userId: string,
    @Query() query: QueryActivitiesDto,
  ) {
    // Implementation
    return {};
  }

  /**
   * Get user integration preferences
   */
  @Get('preferences/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Get user preferences',
    description: `Retrieves the current integration preferences for a user, including
    which applications are enabled, sync settings, and intelligence levels.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Preferences retrieved successfully',
    type: PreferencesResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User preferences not found',
    type: ErrorResponseDto,
  })
  async getPreferences(@Param('userId') userId: string): Promise<PreferencesResponseDto> {
    // Implementation
    return {} as PreferencesResponseDto;
  }

  /**
   * Update user integration preferences
   */
  @Put('preferences/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Update user preferences',
    description: `Updates integration preferences for a user. Only provided fields
    are updated; omitted fields retain their current values.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiBody({
    type: UpdatePreferencesDto,
    description: 'Preferences to update',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Preferences updated successfully',
    type: PreferencesResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid preferences data',
    type: ErrorResponseDto,
  })
  async updatePreferences(
    @Param('userId') userId: string,
    @Body() updatePreferencesDto: UpdatePreferencesDto,
  ): Promise<PreferencesResponseDto> {
    // Implementation
    return {} as PreferencesResponseDto;
  }

  /**
   * Sync user data across applications
   */
  @Post('sync/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Sync user data',
    description: `Triggers synchronization of user data across integrated applications.
    Can sync all enabled applications or specific ones. Synchronization happens
    asynchronously and progress can be tracked via the sync status endpoint.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiBody({
    type: SyncRequestDto,
    required: false,
    description: 'Sync options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sync initiated successfully',
    schema: {
      type: 'object',
      properties: {
        syncId: { type: 'string', example: 'sync-456' },
        status: { type: 'string', example: 'initiated' },
        applications: {
          type: 'array',
          items: { type: 'string' },
          example: ['e-connect', 'lighthouse'],
        },
        estimatedTime: { type: 'string', example: '30 seconds' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.TOO_MANY_REQUESTS,
    description: 'Sync rate limit exceeded',
    type: RateLimitErrorDto,
  })
  async syncUserData(
    @Param('userId') userId: string,
    @Body() syncRequestDto?: SyncRequestDto,
  ) {
    // Implementation
    return {};
  }

  /**
   * Get sync status
   */
  @Get('sync/:userId/status')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Get sync status',
    description: `Retrieves the current synchronization status for a user,
    including last sync times and any errors for each application.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sync status retrieved',
    type: SyncStatusResponseDto,
  })
  async getSyncStatus(@Param('userId') userId: string): Promise<SyncStatusResponseDto> {
    // Implementation
    return {} as SyncStatusResponseDto;
  }

  /**
   * Get federated data from all applications
   */
  @Get('federated/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Get federated data',
    description: `Retrieves aggregated data from all integrated applications for a user.
    AMNA analyzes this data to provide cross-application insights and recommendations.
    This endpoint implements smart caching to optimize performance.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiQuery({
    name: 'includeInsights',
    required: false,
    type: Boolean,
    description: 'Include AI-generated insights',
    example: true,
  })
  @ApiQuery({
    name: 'applications',
    required: false,
    isArray: true,
    description: 'Specific applications to include',
    example: ['e-connect', 'lighthouse'],
  })
  @ApiHeader({
    name: 'X-Cache-Control',
    description: 'Cache control directive',
    required: false,
    example: 'no-cache',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Federated data retrieved',
    type: FederatedDataResponseDto,
    headers: {
      'X-Cache-Status': {
        description: 'Cache hit/miss status',
        schema: { type: 'string', example: 'HIT' },
      },
    },
  })
  async getFederatedData(
    @Param('userId') userId: string,
    @Query('includeInsights') includeInsights?: boolean,
    @Query('applications') applications?: string[],
  ): Promise<FederatedDataResponseDto> {
    // Implementation
    return {} as FederatedDataResponseDto;
  }

  /**
   * Health check endpoint
   */
  @Get('health')
  @ApiOperation({
    summary: 'Health check',
    description: `Performs comprehensive health checks on all integration services,
    including database connections, Redis availability, and external application APIs.
    No authentication required for monitoring purposes.`,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'System is healthy',
    type: HealthStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'System is unhealthy',
    type: HealthStatusResponseDto,
  })
  async healthCheck(): Promise<HealthStatusResponseDto> {
    // Implementation
    return {} as HealthStatusResponseDto;
  }

  /**
   * Delete user data (GDPR)
   */
  @Delete('gdpr/user/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Delete user data (GDPR)',
    description: `Implements the right to be forgotten by scheduling deletion of all
    user data across integrated applications. Deletion is performed asynchronously
    and includes audit logging for compliance.`,
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user-123',
  })
  @ApiHeader({
    name: 'X-Deletion-Reason',
    description: 'Reason for deletion',
    required: true,
    example: 'user-request',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Deletion scheduled',
    schema: {
      type: 'object',
      properties: {
        deletionId: { type: 'string', example: 'del-789' },
        status: { type: 'string', example: 'scheduled' },
        scheduledFor: { type: 'string', example: '2024-01-01T00:00:00Z' },
        applications: {
          type: 'array',
          items: { type: 'string' },
          example: ['e-connect', 'lighthouse', 'training', 'vendors', 'wins-of-week'],
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
    type: ErrorResponseDto,
  })
  async deleteUserData(@Param('userId') userId: string) {
    // Implementation
    return {};
  }

  /**
   * Export user data (GDPR)
   */
  @Post('gdpr/export')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Export user data (GDPR)',
    description: `Initiates export of all user data from integrated applications.
    The export is processed asynchronously and includes data from all enabled
    applications in a standardized format.`,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        userId: { type: 'string', example: 'user-123' },
        format: { type: 'string', enum: ['json', 'csv'], example: 'json' },
        includeDeleted: { type: 'boolean', example: false },
      },
      required: ['userId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Export initiated',
    schema: {
      type: 'object',
      properties: {
        exportId: { type: 'string', example: 'exp-321' },
        status: { type: 'string', example: 'processing' },
        estimatedTime: { type: 'string', example: '5 minutes' },
        downloadUrl: { type: 'string', example: '/api/integration/gdpr/export/exp-321' },
      },
    },
  })
  async exportUserData(@Body() exportDto: any) {
    // Implementation
    return {};
  }
}