import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { EnhancedRateLimitGuard, CircuitBreakerRateLimitGuard } from '../guards/enhanced-rate-limit.guard';
import { RateLimit, RateLimitPresets } from '../decorators/rate-limit.decorator';

/**
 * Example controller showing various rate limiting strategies
 */
@ApiTags('integration-rate-limited')
@Controller('api/integration/rate-limited')
@UseGuards(AuthGuard('jwt'))
export class RateLimitedIntegrationController {

  /**
   * Standard rate limiting for regular endpoints
   */
  @Get('status/:userId')
  @ApiOperation({ summary: 'Get user integration status' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.standard())
  async getUserStatus(@Param('userId') userId: string) {
    return {
      userId,
      status: 'active',
      integrations: ['e-connect', 'lighthouse', 'training'],
    };
  }

  /**
   * Strict rate limiting for sensitive operations
   */
  @Post('sync-all')
  @ApiOperation({ summary: 'Sync all user data (rate limited)' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.strict())
  async syncAllData(@Body() syncDto: any) {
    return {
      message: 'Sync initiated',
      estimatedTime: '5 minutes',
    };
  }

  /**
   * Per-user rate limiting
   */
  @Post('activity')
  @ApiOperation({ summary: 'Create activity with per-user rate limit' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.perUser(50))
  async createActivity(@Body() activityDto: any) {
    return {
      id: 'activity-123',
      created: true,
    };
  }

  /**
   * Per-application rate limiting for integration endpoints
   */
  @Post('webhook/:application')
  @ApiOperation({ summary: 'Webhook endpoint with per-app rate limit' })
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.perApplication(1000))
  async handleWebhook(
    @Param('application') application: string,
    @Body() webhookData: any,
  ) {
    return {
      received: true,
      application,
      processed: new Date(),
    };
  }

  /**
   * Circuit breaker protection for critical services
   */
  @Post('federate-data')
  @ApiOperation({ summary: 'Federate data with circuit breaker protection' })
  @ApiBearerAuth()
  @UseGuards(CircuitBreakerRateLimitGuard)
  @RateLimit({
    windowMs: 60000,
    maxRequests: 100,
    enableExponentialBackoff: true,
  })
  async federateData(@Body() federationDto: any) {
    return {
      federated: true,
      sources: federationDto.sources,
    };
  }

  /**
   * Burst rate limiting for real-time features
   */
  @Post('realtime-update')
  @ApiOperation({ summary: 'Real-time update with burst protection' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.burst(100, 20))
  async realtimeUpdate(@Body() updateDto: any) {
    return {
      updated: true,
      timestamp: new Date(),
    };
  }

  /**
   * File upload with specific rate limiting
   */
  @Post('upload')
  @ApiOperation({ summary: 'Upload file with rate limiting' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.fileUpload())
  async uploadFile(@Body() fileDto: any) {
    return {
      uploaded: true,
      fileId: 'file-123',
    };
  }

  /**
   * Custom rate limiting with multiple strategies
   */
  @Post('complex-operation')
  @ApiOperation({ summary: 'Complex operation with custom rate limits' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit({
    windowMs: 300000, // 5 minutes
    maxRequests: 10,
    keyGenerator: (request) => {
      // Custom key based on user and operation type
      const userId = request.user?.id || 'anonymous';
      const operation = request.body?.operationType || 'default';
      return `complex:${userId}:${operation}`;
    },
    onLimitReached: (request) => {
      // Custom logging or alerting
      console.error(`Rate limit reached for complex operation: ${request.user?.id}`);
    },
    enableExponentialBackoff: true,
    backoffMultiplier: 3,
    maxBackoffMs: 1800000, // 30 minutes
  })
  async complexOperation(@Body() operationDto: any) {
    return {
      result: 'Complex operation completed',
      operationType: operationDto.operationType,
    };
  }

  /**
   * WebSocket rate limiting example
   */
  @Post('websocket-message')
  @ApiOperation({ summary: 'WebSocket message with rate limiting' })
  @ApiBearerAuth()
  @UseGuards(EnhancedRateLimitGuard)
  @RateLimit(RateLimitPresets.websocket())
  async sendWebSocketMessage(@Body() messageDto: any) {
    return {
      sent: true,
      messageId: 'msg-123',
    };
  }

  /**
   * Endpoint without rate limiting (for comparison)
   */
  @Get('health')
  @ApiOperation({ summary: 'Health check (no rate limit)' })
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date(),
    };
  }
}