import { 
  Injectable, 
  CanActivate, 
  ExecutionContext, 
  HttpException, 
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';

export interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyPrefix?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: any) => string;
  handler?: (request: any, response: any) => void;
  onLimitReached?: (request: any) => void;
  enableExponentialBackoff?: boolean;
  backoffMultiplier?: number;
  maxBackoffMs?: number;
}

export const RATE_LIMIT_KEY = 'rate-limit-options';

@Injectable()
export class EnhancedRateLimitGuard implements CanActivate {
  private readonly defaultOptions: RateLimitOptions = {
    windowMs: 60000, // 1 minute
    maxRequests: 100,
    keyPrefix: 'rate-limit:',
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    enableExponentialBackoff: true,
    backoffMultiplier: 2,
    maxBackoffMs: 300000, // 5 minutes
  };

  constructor(
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Get rate limit options from decorator or use defaults
    const options = this.getRateLimitOptions(context);
    
    // Generate unique key for the client
    const key = this.generateKey(request, options);
    
    // Check current rate limit status
    const limitStatus = await this.checkRateLimit(key, options);
    
    if (!limitStatus.allowed) {
      // Set rate limit headers
      this.setRateLimitHeaders(response, limitStatus);
      
      // Call custom handler if provided
      if (options.onLimitReached) {
        options.onLimitReached(request);
      }

      // Apply exponential backoff if enabled
      if (options.enableExponentialBackoff) {
        await this.applyExponentialBackoff(key, options);
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Too many requests',
          error: 'Rate limit exceeded',
          retryAfter: limitStatus.resetTime,
          remainingRequests: 0,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Set rate limit headers for successful requests
    this.setRateLimitHeaders(response, limitStatus);

    // Track request for rate limiting
    await this.incrementRequestCount(key, options);

    return true;
  }

  private getRateLimitOptions(context: ExecutionContext): RateLimitOptions {
    const handlerOptions = this.reflector.get<RateLimitOptions>(
      RATE_LIMIT_KEY,
      context.getHandler(),
    );
    const classOptions = this.reflector.get<RateLimitOptions>(
      RATE_LIMIT_KEY,
      context.getClass(),
    );

    // Merge options with priority: handler > class > default
    return {
      ...this.defaultOptions,
      ...classOptions,
      ...handlerOptions,
    };
  }

  private generateKey(request: any, options: RateLimitOptions): string {
    if (options.keyGenerator) {
      return `${options.keyPrefix}${options.keyGenerator(request)}`;
    }

    // Default key generation based on user ID or IP
    const userId = request.user?.id;
    const ip = request.ip || request.connection.remoteAddress;
    const endpoint = `${request.method}:${request.route.path}`;

    if (userId) {
      return `${options.keyPrefix}user:${userId}:${endpoint}`;
    }

    return `${options.keyPrefix}ip:${ip}:${endpoint}`;
  }

  private async checkRateLimit(
    key: string,
    options: RateLimitOptions,
  ): Promise<{
    allowed: boolean;
    count: number;
    resetTime: number;
    remainingRequests: number;
  }> {
    const now = Date.now();
    const windowStart = now - options.windowMs;

    // Check if in backoff period
    const backoffKey = `${key}:backoff`;
    const backoffUntil = await this.redis.get(backoffKey);
    if (backoffUntil && parseInt(backoffUntil) > now) {
      return {
        allowed: false,
        count: options.maxRequests,
        resetTime: parseInt(backoffUntil),
        remainingRequests: 0,
      };
    }

    // Remove old entries outside the window
    await this.redis.zremrangebyscore(key, '-inf', windowStart);

    // Count requests in current window
    const count = await this.redis.zcard(key);

    // Calculate reset time
    const oldestEntry = await this.redis.zrange(key, 0, 0, 'WITHSCORES');
    const resetTime = oldestEntry.length > 1
      ? parseInt(oldestEntry[1]) + options.windowMs
      : now + options.windowMs;

    return {
      allowed: count < options.maxRequests,
      count,
      resetTime,
      remainingRequests: Math.max(0, options.maxRequests - count),
    };
  }

  private async incrementRequestCount(
    key: string,
    options: RateLimitOptions,
  ): Promise<void> {
    const now = Date.now();
    
    // Add current request to sorted set
    await this.redis.zadd(key, now, `${now}:${Math.random()}`);
    
    // Set expiration
    await this.redis.expire(key, Math.ceil(options.windowMs / 1000));
  }

  private async applyExponentialBackoff(
    key: string,
    options: RateLimitOptions,
  ): Promise<void> {
    const backoffKey = `${key}:backoff`;
    const failureKey = `${key}:failures`;

    // Increment failure count
    const failures = await this.redis.incr(failureKey);
    await this.redis.expire(failureKey, Math.ceil(options.windowMs / 1000));

    // Calculate backoff duration
    const backoffMs = Math.min(
      options.windowMs * Math.pow(options.backoffMultiplier!, failures),
      options.maxBackoffMs!,
    );

    // Set backoff period
    const backoffUntil = Date.now() + backoffMs;
    await this.redis.set(
      backoffKey,
      backoffUntil,
      'PX',
      backoffMs,
    );
  }

  private setRateLimitHeaders(response: any, limitStatus: any): void {
    response.setHeader('X-RateLimit-Limit', limitStatus.count);
    response.setHeader('X-RateLimit-Remaining', limitStatus.remainingRequests);
    response.setHeader('X-RateLimit-Reset', new Date(limitStatus.resetTime).toISOString());
    
    if (!limitStatus.allowed) {
      response.setHeader('Retry-After', Math.ceil((limitStatus.resetTime - Date.now()) / 1000));
    }
  }
}

/**
 * Circuit breaker for cascade failure prevention
 */
@Injectable()
export class CircuitBreakerRateLimitGuard extends EnhancedRateLimitGuard {
  private readonly circuitBreakerOptions = {
    failureThreshold: 5,
    resetTimeout: 60000, // 1 minute
    halfOpenRequests: 3,
  };

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const circuitKey = this.getCircuitKey(request);
    
    // Check circuit breaker state
    const state = await this.getCircuitState(circuitKey);
    
    if (state === 'open') {
      throw new HttpException(
        {
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
          message: 'Service temporarily unavailable',
          error: 'Circuit breaker open',
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    if (state === 'half-open') {
      const allowed = await this.checkHalfOpenState(circuitKey);
      if (!allowed) {
        throw new HttpException(
          {
            statusCode: HttpStatus.SERVICE_UNAVAILABLE,
            message: 'Service temporarily unavailable',
            error: 'Circuit breaker half-open limit reached',
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }
    }

    try {
      const result = await super.canActivate(context);
      
      // Reset failures on success
      if (result) {
        await this.recordSuccess(circuitKey);
      }
      
      return result;
    } catch (error) {
      // Record failure
      await this.recordFailure(circuitKey);
      throw error;
    }
  }

  private getCircuitKey(request: any): string {
    const service = request.headers['x-source-application'] || 'unknown';
    return `circuit-breaker:${service}`;
  }

  private async getCircuitState(key: string): Promise<'closed' | 'open' | 'half-open'> {
    const stateData = await this.redis.hgetall(key);
    
    if (!stateData.state) {
      return 'closed';
    }

    const now = Date.now();
    const lastFailure = parseInt(stateData.lastFailure || '0');
    
    if (stateData.state === 'open' && now - lastFailure > this.circuitBreakerOptions.resetTimeout) {
      // Transition to half-open
      await this.redis.hset(key, 'state', 'half-open');
      await this.redis.hset(key, 'halfOpenRequests', '0');
      return 'half-open';
    }

    return stateData.state as any;
  }

  private async checkHalfOpenState(key: string): Promise<boolean> {
    const requests = await this.redis.hincrby(key, 'halfOpenRequests', 1);
    return requests <= this.circuitBreakerOptions.halfOpenRequests;
  }

  private async recordSuccess(key: string): Promise<void> {
    const state = await this.redis.hget(key, 'state');
    
    if (state === 'half-open') {
      // Transition to closed
      await this.redis.del(key);
    }
  }

  private async recordFailure(key: string): Promise<void> {
    const failures = await this.redis.hincrby(key, 'failures', 1);
    await this.redis.hset(key, 'lastFailure', Date.now());
    
    if (failures >= this.circuitBreakerOptions.failureThreshold) {
      // Open circuit
      await this.redis.hset(key, 'state', 'open');
    }
    
    // Set expiration
    await this.redis.expire(key, Math.ceil(this.circuitBreakerOptions.resetTimeout / 1000) * 2);
  }
}