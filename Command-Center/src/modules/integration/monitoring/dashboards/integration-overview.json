{"dashboard": {"id": null, "uid": "integration-overview", "title": "AMNA Integration Overview", "tags": ["integration", "amna", "overview"], "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "10s", "panels": [{"id": 1, "gridPos": {"x": 0, "y": 0, "w": 6, "h": 8}, "type": "stat", "title": "Total Active Integrations", "targets": [{"expr": "count(up{job=~\"e-connect|lighthouse|training|vendors|wins\"} == 1)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 3}, {"color": "green", "value": 5}]}, "unit": "none"}}}, {"id": 2, "gridPos": {"x": 6, "y": 0, "w": 6, "h": 8}, "type": "stat", "title": "Messages Processed (24h)", "targets": [{"expr": "sum(increase(amna_messages_processed_total[24h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "decimals": 0}}}, {"id": 3, "gridPos": {"x": 12, "y": 0, "w": 6, "h": 8}, "type": "stat", "title": "Average Response Time", "targets": [{"expr": "avg(rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 1}]}, "unit": "s"}}}, {"id": 4, "gridPos": {"x": 18, "y": 0, "w": 6, "h": 8}, "type": "stat", "title": "Error Rate", "targets": [{"expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m])) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}}, {"id": 5, "gridPos": {"x": 0, "y": 8, "w": 12, "h": 8}, "type": "graph", "title": "Request Rate by Integration", "targets": [{"expr": "sum by (source) (rate(integration_requests_total[5m]))", "legendFormat": "{{ source }}", "refId": "A"}], "xaxis": {"mode": "time"}, "yaxis": {"format": "reqps", "label": "Requests/sec"}}, {"id": 6, "gridPos": {"x": 12, "y": 8, "w": 12, "h": 8}, "type": "graph", "title": "WebSocket Connections", "targets": [{"expr": "websocket_connections_active", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "rate(websocket_messages_total[5m])", "legendFormat": "Messages/sec", "refId": "B"}], "xaxis": {"mode": "time"}, "yaxes": [{"format": "short", "label": "Connections"}, {"format": "short", "label": "Messages/sec"}]}, {"id": 7, "gridPos": {"x": 0, "y": 16, "w": 8, "h": 8}, "type": "piechart", "title": "Activity Distribution by Source", "targets": [{"expr": "sum by (source) (increase(integration_activities_total[1h]))", "legendFormat": "{{ source }}", "refId": "A"}]}, {"id": 8, "gridPos": {"x": 8, "y": 16, "w": 8, "h": 8}, "type": "heatmap", "title": "Response Time Heatmap", "targets": [{"expr": "sum(increase(http_request_duration_seconds_bucket[5m])) by (le)", "format": "heatmap", "refId": "A"}], "dataFormat": "timeseries", "yAxis": {"format": "s"}}, {"id": 9, "gridPos": {"x": 16, "y": 16, "w": 8, "h": 8}, "type": "alertlist", "title": "Active Alerts", "options": {"showOptions": "current", "maxItems": 10, "sortOrder": 3, "dashboardAlerts": true, "alertName": "", "dashboardTitle": "", "tags": ["integration"]}}]}}