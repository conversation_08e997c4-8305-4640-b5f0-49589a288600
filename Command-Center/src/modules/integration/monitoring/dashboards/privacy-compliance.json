{"dashboard": {"id": null, "uid": "privacy-compliance", "title": "Privacy & Compliance Dashboard", "tags": ["privacy", "gdpr", "compliance"], "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "1m", "panels": [{"id": 1, "gridPos": {"x": 0, "y": 0, "w": 6, "h": 6}, "type": "stat", "title": "Active Privacy Requests", "targets": [{"expr": "sum(privacy_requests_active)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "none"}}}, {"id": 2, "gridPos": {"x": 6, "y": 0, "w": 6, "h": 6}, "type": "stat", "title": "Data Deletion Requests (30d)", "targets": [{"expr": "sum(increase(privacy_deletion_requests_total[30d]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "none", "decimals": 0}}}, {"id": 3, "gridPos": {"x": 12, "y": 0, "w": 6, "h": 6}, "type": "stat", "title": "Consent Compliance Rate", "targets": [{"expr": "(sum(consent_granted_total) / sum(consent_requested_total)) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}, "unit": "percent"}}}, {"id": 4, "gridPos": {"x": 18, "y": 0, "w": 6, "h": 6}, "type": "stat", "title": "Avg Request Processing Time", "targets": [{"expr": "avg(privacy_request_processing_duration_seconds)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 3600}, {"color": "red", "value": 86400}]}, "unit": "s"}}}, {"id": 5, "gridPos": {"x": 0, "y": 6, "w": 12, "h": 8}, "type": "graph", "title": "Privacy Requests by Type", "targets": [{"expr": "sum by (type) (rate(privacy_requests_total[5m]))", "legendFormat": "{{ type }}", "refId": "A"}], "xaxis": {"mode": "time"}, "yaxis": {"format": "short", "label": "Requests/min"}}, {"id": 6, "gridPos": {"x": 12, "y": 6, "w": 12, "h": 8}, "type": "graph", "title": "Consent Management Trends", "targets": [{"expr": "sum by (consent_type) (increase(consent_granted_total[1h]))", "legendFormat": "Granted: {{ consent_type }}", "refId": "A"}, {"expr": "sum by (consent_type) (increase(consent_withdrawn_total[1h]))", "legendFormat": "Withdrawn: {{ consent_type }}", "refId": "B"}], "xaxis": {"mode": "time"}, "yaxis": {"format": "short", "label": "Count"}}, {"id": 7, "gridPos": {"x": 0, "y": 14, "w": 8, "h": 8}, "type": "piechart", "title": "Data Subject Request Distribution", "targets": [{"expr": "sum by (type) (privacy_requests_total)", "legendFormat": "{{ type }}", "refId": "A"}]}, {"id": 8, "gridPos": {"x": 8, "y": 14, "w": 8, "h": 8}, "type": "table", "title": "Recent Privacy Incidents", "targets": [{"expr": "topk(10, privacy_incidents_total)", "format": "table", "instant": true, "refId": "A"}]}, {"id": 9, "gridPos": {"x": 16, "y": 14, "w": 8, "h": 8}, "type": "bargauge", "title": "Compliance Status by Category", "targets": [{"expr": "compliance_score_by_category", "refId": "A"}], "options": {"orientation": "horizontal", "displayMode": "gradient", "showUnfilled": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100}}}, {"id": 10, "gridPos": {"x": 0, "y": 22, "w": 24, "h": 6}, "type": "alert-list", "title": "Privacy & Compliance Alerts", "options": {"showOptions": "current", "maxItems": 10, "sortOrder": 1, "dashboardAlerts": false, "alertName": "", "dashboardTitle": "", "tags": ["privacy", "compliance"], "stateFilter": {"ok": false, "paused": false, "no_data": false, "exec_error": false, "pending": true, "alerting": true}}}]}}