{"dashboard": {"id": null, "uid": "business-metrics", "title": "Business Metrics Dashboard", "tags": ["business", "metrics", "kpi"], "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "30s", "panels": [{"id": 1, "gridPos": {"x": 0, "y": 0, "w": 8, "h": 6}, "type": "stat", "title": "Daily Active Users", "targets": [{"expr": "count(count by (user_id) (increase(user_activity_total[24h]) > 0))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "none", "decimals": 0}}}, {"id": 2, "gridPos": {"x": 8, "y": 0, "w": 8, "h": 6}, "type": "stat", "title": "Weekly Active Users", "targets": [{"expr": "count(count by (user_id) (increase(user_activity_total[7d]) > 0))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "none", "decimals": 0}}}, {"id": 3, "gridPos": {"x": 16, "y": 0, "w": 8, "h": 6}, "type": "stat", "title": "User Engagement Rate", "targets": [{"expr": "(count(count by (user_id) (increase(user_activity_total[24h]) > 5)) / count(count by (user_id) (increase(user_activity_total[24h]) > 0))) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 30}, {"color": "green", "value": 60}]}, "unit": "percent"}}}, {"id": 4, "gridPos": {"x": 0, "y": 6, "w": 12, "h": 8}, "type": "graph", "title": "User Activity Trend", "targets": [{"expr": "sum(increase(user_activity_total[1h]))", "legendFormat": "Total Activities", "refId": "A"}, {"expr": "count(count by (user_id) (increase(user_activity_total[1h]) > 0))", "legendFormat": "Active Users", "refId": "B"}], "xaxis": {"mode": "time"}, "yaxes": [{"format": "short", "label": "Count"}, {"format": "short", "label": "Users"}]}, {"id": 5, "gridPos": {"x": 12, "y": 6, "w": 12, "h": 8}, "type": "graph", "title": "Feature Adoption", "targets": [{"expr": "sum by (feature) (increase(feature_usage_total[1d]))", "legendFormat": "{{ feature }}", "refId": "A"}], "xaxis": {"mode": "time"}, "yaxis": {"format": "short", "label": "Usage Count"}}, {"id": 6, "gridPos": {"x": 0, "y": 14, "w": 8, "h": 8}, "type": "bargauge", "title": "Integration Usage by Application", "targets": [{"expr": "sum by (application) (increase(integration_activities_total[24h]))", "refId": "A"}], "options": {"orientation": "horizontal", "displayMode": "gradient", "showUnfilled": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}, {"color": "green", "value": 100}, {"color": "yellow", "value": 500}, {"color": "orange", "value": 1000}]}, "unit": "short"}}}, {"id": 7, "gridPos": {"x": 8, "y": 14, "w": 8, "h": 8}, "type": "table", "title": "Top Users by Activity", "targets": [{"expr": "topk(10, sum by (user_id) (increase(user_activity_total[24h])))", "format": "table", "instant": true, "refId": "A"}]}, {"id": 8, "gridPos": {"x": 16, "y": 14, "w": 8, "h": 8}, "type": "stat", "title": "Customer Satisfaction Score", "targets": [{"expr": "avg(user_satisfaction_score)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 3}, {"color": "green", "value": 4}]}, "unit": "none", "min": 0, "max": 5, "decimals": 1}}, "options": {"graphMode": "area", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}}}, {"id": 9, "gridPos": {"x": 0, "y": 22, "w": 24, "h": 8}, "type": "graph", "title": "Revenue Impact Metrics", "targets": [{"expr": "sum(revenue_generated_total)", "legendFormat": "Total Revenue", "refId": "A"}, {"expr": "sum(cost_savings_total)", "legendFormat": "Cost Savings", "refId": "B"}, {"expr": "sum(efficiency_improvement_percentage)", "legendFormat": "Efficiency Improvement %", "refId": "C"}], "xaxis": {"mode": "time"}, "yaxes": [{"format": "currencyUSD", "label": "Value"}, {"format": "percent", "label": "Percentage"}]}]}}