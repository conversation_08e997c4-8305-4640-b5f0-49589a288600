import { Injectable, Logger } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { PrometheusService } from '@willsoto/nestjs-prometheus';
import { Counter, Gauge, Histogram, Summary } from 'prom-client';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class BusinessMetricsService {
  private readonly logger = new Logger(BusinessMetricsService.name);

  constructor(
    private readonly prometheusService: PrometheusService,
    private readonly eventEmitter: EventEmitter2,
    @InjectMetric('user_activity_total') private userActivityCounter: Counter<string>,
    @InjectMetric('integration_activities_total') private integrationActivitiesCounter: Counter<string>,
    @InjectMetric('feature_usage_total') private featureUsageCounter: Counter<string>,
    @InjectMetric('revenue_generated_total') private revenueCounter: Counter<string>,
    @InjectMetric('cost_savings_total') private costSavingsCounter: Counter<string>,
    @InjectMetric('efficiency_improvement_percentage') private efficiencyGauge: Gauge<string>,
    @InjectMetric('user_satisfaction_score') private satisfactionGauge: Gauge<string>,
    @InjectMetric('privacy_requests_total') private privacyRequestsCounter: Counter<string>,
    @InjectMetric('privacy_requests_active') private activePrivacyRequestsGauge: Gauge<string>,
    @InjectMetric('privacy_deletion_requests_total') private deletionRequestsCounter: Counter<string>,
    @InjectMetric('consent_granted_total') private consentGrantedCounter: Counter<string>,
    @InjectMetric('consent_withdrawn_total') private consentWithdrawnCounter: Counter<string>,
    @InjectMetric('consent_requested_total') private consentRequestedCounter: Counter<string>,
    @InjectMetric('privacy_request_processing_duration_seconds') private privacyProcessingDuration: Histogram<string>,
    @InjectMetric('compliance_score_by_category') private complianceScoreGauge: Gauge<string>,
    @InjectMetric('privacy_incidents_total') private privacyIncidentsCounter: Counter<string>,
    @InjectMetric('active_users_daily') private dailyActiveUsersGauge: Gauge<string>,
    @InjectMetric('active_users_weekly') private weeklyActiveUsersGauge: Gauge<string>,
    @InjectMetric('user_engagement_rate') private engagementRateGauge: Gauge<string>,
  ) {
    this.initializeMetrics();
  }

  private initializeMetrics() {
    // Set initial compliance scores
    this.complianceScoreGauge.set({ category: 'data_protection' }, 95);
    this.complianceScoreGauge.set({ category: 'consent_management' }, 92);
    this.complianceScoreGauge.set({ category: 'data_retention' }, 88);
    this.complianceScoreGauge.set({ category: 'security_measures' }, 94);
    this.complianceScoreGauge.set({ category: 'transparency' }, 90);
  }

  /**
   * Track user activity
   */
  @OnEvent('user.activity')
  trackUserActivity(event: {
    userId: string;
    activity: string;
    source: string;
    metadata?: any;
  }) {
    this.userActivityCounter.inc({
      user_id: event.userId,
      activity: event.activity,
      source: event.source,
    });

    // Track feature usage
    if (event.metadata?.feature) {
      this.featureUsageCounter.inc({
        feature: event.metadata.feature,
        user_id: event.userId,
      });
    }
  }

  /**
   * Track integration activity
   */
  @OnEvent('integration.activity')
  trackIntegrationActivity(event: {
    application: string;
    activity: string;
    userId?: string;
  }) {
    this.integrationActivitiesCounter.inc({
      application: event.application,
      activity: event.activity,
    });
  }

  /**
   * Track privacy requests
   */
  @OnEvent('privacy.request.created')
  trackPrivacyRequest(event: {
    request: { type: string; userId: string };
    timestamp: Date;
  }) {
    this.privacyRequestsCounter.inc({
      type: event.request.type,
    });

    this.activePrivacyRequestsGauge.inc();

    if (event.request.type === 'data_deletion') {
      this.deletionRequestsCounter.inc();
    }
  }

  /**
   * Track privacy request completion
   */
  @OnEvent('privacy.request.status.updated')
  trackPrivacyRequestUpdate(event: {
    requestId: string;
    status: string;
    metadata?: any;
  }) {
    if (event.status === 'completed' || event.status === 'failed') {
      this.activePrivacyRequestsGauge.dec();
    }
  }

  /**
   * Track consent events
   */
  @OnEvent('consent.recorded')
  trackConsentRecorded(event: {
    userId: string;
    type: string;
    status: string;
  }) {
    this.consentRequestedCounter.inc({ consent_type: event.type });

    if (event.status === 'granted') {
      this.consentGrantedCounter.inc({ consent_type: event.type });
    }
  }

  @OnEvent('consent.withdrawn')
  trackConsentWithdrawn(event: {
    userId: string;
    type: string;
  }) {
    this.consentWithdrawnCounter.inc({ consent_type: event.type });
  }

  /**
   * Track revenue metrics
   */
  trackRevenueMetric(amount: number, source: string, userId?: string) {
    this.revenueCounter.inc({ source }, amount);
  }

  /**
   * Track cost savings
   */
  trackCostSavings(amount: number, category: string) {
    this.costSavingsCounter.inc({ category }, amount);
  }

  /**
   * Update efficiency metrics
   */
  updateEfficiencyMetric(percentage: number, process: string) {
    this.efficiencyGauge.set({ process }, percentage);
  }

  /**
   * Update satisfaction score
   */
  updateSatisfactionScore(score: number, userId: string) {
    this.satisfactionGauge.set({ user_id: userId }, score);
  }

  /**
   * Track privacy processing duration
   */
  trackPrivacyProcessingDuration(type: string, durationMs: number) {
    this.privacyProcessingDuration.observe(
      { type },
      durationMs / 1000, // Convert to seconds
    );
  }

  /**
   * Update compliance score
   */
  updateComplianceScore(category: string, score: number) {
    this.complianceScoreGauge.set({ category }, score);
  }

  /**
   * Track privacy incident
   */
  trackPrivacyIncident(severity: string, type: string) {
    this.privacyIncidentsCounter.inc({ severity, type });
  }

  /**
   * Update daily active users
   */
  @Interval(60000) // Every minute
  async updateActiveUserMetrics() {
    try {
      // This would query your database for actual user counts
      // For now, using placeholder logic
      const dailyActiveUsers = await this.calculateDailyActiveUsers();
      const weeklyActiveUsers = await this.calculateWeeklyActiveUsers();
      const engagementRate = await this.calculateEngagementRate();

      this.dailyActiveUsersGauge.set(dailyActiveUsers);
      this.weeklyActiveUsersGauge.set(weeklyActiveUsers);
      this.engagementRateGauge.set(engagementRate);
    } catch (error) {
      this.logger.error('Failed to update active user metrics', error);
    }
  }

  /**
   * Generate business insights
   */
  async generateBusinessInsights(): Promise<{
    summary: any;
    trends: any;
    recommendations: string[];
  }> {
    const metrics = await this.prometheusService.registry.getMetricsAsJSON();

    // Analyze metrics to generate insights
    const summary = {
      totalUsers: await this.getTotalUsers(),
      activeUsersToday: await this.calculateDailyActiveUsers(),
      engagementRate: await this.calculateEngagementRate(),
      topFeatures: await this.getTopFeatures(),
      revenueGrowth: await this.calculateRevenueGrowth(),
      complianceScore: await this.calculateOverallComplianceScore(),
    };

    const trends = {
      userGrowth: await this.analyzeUserGrowthTrend(),
      featureAdoption: await this.analyzeFeatureAdoptionTrend(),
      privacyRequests: await this.analyzePrivacyRequestTrend(),
    };

    const recommendations = this.generateRecommendations(summary, trends);

    return { summary, trends, recommendations };
  }

  // Private helper methods

  private async calculateDailyActiveUsers(): Promise<number> {
    // Placeholder - implement actual calculation
    return Math.floor(Math.random() * 1000) + 500;
  }

  private async calculateWeeklyActiveUsers(): Promise<number> {
    // Placeholder - implement actual calculation
    return Math.floor(Math.random() * 5000) + 2000;
  }

  private async calculateEngagementRate(): Promise<number> {
    // Placeholder - implement actual calculation
    return Math.random() * 30 + 50; // 50-80%
  }

  private async getTotalUsers(): Promise<number> {
    // Placeholder - implement actual calculation
    return Math.floor(Math.random() * 10000) + 5000;
  }

  private async getTopFeatures(): Promise<string[]> {
    // Placeholder - implement actual calculation
    return ['email-integration', 'ai-insights', 'research-tools', 'reporting'];
  }

  private async calculateRevenueGrowth(): Promise<number> {
    // Placeholder - implement actual calculation
    return Math.random() * 20 + 5; // 5-25% growth
  }

  private async calculateOverallComplianceScore(): Promise<number> {
    // Placeholder - implement actual calculation
    return 91.8;
  }

  private async analyzeUserGrowthTrend(): Promise<string> {
    // Placeholder - implement actual analysis
    return 'increasing';
  }

  private async analyzeFeatureAdoptionTrend(): Promise<string> {
    // Placeholder - implement actual analysis
    return 'stable';
  }

  private async analyzePrivacyRequestTrend(): Promise<string> {
    // Placeholder - implement actual analysis
    return 'decreasing';
  }

  private generateRecommendations(summary: any, trends: any): string[] {
    const recommendations: string[] = [];

    if (summary.engagementRate < 60) {
      recommendations.push('Consider improving user onboarding to increase engagement');
    }

    if (trends.privacyRequests === 'increasing') {
      recommendations.push('Review privacy practices to reduce request volume');
    }

    if (summary.complianceScore < 90) {
      recommendations.push('Focus on improving compliance in weak areas');
    }

    if (trends.userGrowth === 'decreasing') {
      recommendations.push('Implement user retention strategies');
    }

    return recommendations;
  }
}