import { Modu<PERSON> } from '@nestjs/common';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { Counter, Gauge, Histogram, Summary } from 'prom-client';
import { BusinessMetricsService } from './business-metrics.service';
import { MonitoringController } from './monitoring.controller';
import { AlertingService } from './alerting.service';
import { MetricsExportService } from './metrics-export.service';

@Module({
  imports: [
    PrometheusModule.register({
      defaultMetrics: {
        enabled: true,
        config: {
          prefix: 'luminar_',
        },
      },
      defaultLabels: {
        app: 'luminar-integration',
      },
    }),
  ],
  providers: [
    BusinessMetricsService,
    AlertingService,
    MetricsExportService,
    // User Activity Metrics
    {
      provide: 'user_activity_total',
      useFactory: () => new Counter({
        name: 'user_activity_total',
        help: 'Total user activities',
        labelNames: ['user_id', 'activity', 'source'],
      }),
    },
    {
      provide: 'active_users_daily',
      useFactory: () => new Gauge({
        name: 'active_users_daily',
        help: 'Number of daily active users',
      }),
    },
    {
      provide: 'active_users_weekly',
      useFactory: () => new Gauge({
        name: 'active_users_weekly',
        help: 'Number of weekly active users',
      }),
    },
    {
      provide: 'user_engagement_rate',
      useFactory: () => new Gauge({
        name: 'user_engagement_rate',
        help: 'User engagement rate percentage',
      }),
    },
    // Integration Metrics
    {
      provide: 'integration_activities_total',
      useFactory: () => new Counter({
        name: 'integration_activities_total',
        help: 'Total integration activities',
        labelNames: ['application', 'activity'],
      }),
    },
    {
      provide: 'integration_requests_total',
      useFactory: () => new Counter({
        name: 'integration_requests_total',
        help: 'Total integration requests',
        labelNames: ['source', 'status'],
      }),
    },
    // Feature Usage Metrics
    {
      provide: 'feature_usage_total',
      useFactory: () => new Counter({
        name: 'feature_usage_total',
        help: 'Total feature usage',
        labelNames: ['feature', 'user_id'],
      }),
    },
    // Business Value Metrics
    {
      provide: 'revenue_generated_total',
      useFactory: () => new Counter({
        name: 'revenue_generated_total',
        help: 'Total revenue generated',
        labelNames: ['source'],
      }),
    },
    {
      provide: 'cost_savings_total',
      useFactory: () => new Counter({
        name: 'cost_savings_total',
        help: 'Total cost savings',
        labelNames: ['category'],
      }),
    },
    {
      provide: 'efficiency_improvement_percentage',
      useFactory: () => new Gauge({
        name: 'efficiency_improvement_percentage',
        help: 'Efficiency improvement percentage',
        labelNames: ['process'],
      }),
    },
    {
      provide: 'user_satisfaction_score',
      useFactory: () => new Gauge({
        name: 'user_satisfaction_score',
        help: 'User satisfaction score (1-5)',
        labelNames: ['user_id'],
      }),
    },
    // Privacy & Compliance Metrics
    {
      provide: 'privacy_requests_total',
      useFactory: () => new Counter({
        name: 'privacy_requests_total',
        help: 'Total privacy requests',
        labelNames: ['type'],
      }),
    },
    {
      provide: 'privacy_requests_active',
      useFactory: () => new Gauge({
        name: 'privacy_requests_active',
        help: 'Number of active privacy requests',
      }),
    },
    {
      provide: 'privacy_deletion_requests_total',
      useFactory: () => new Counter({
        name: 'privacy_deletion_requests_total',
        help: 'Total data deletion requests',
      }),
    },
    {
      provide: 'consent_granted_total',
      useFactory: () => new Counter({
        name: 'consent_granted_total',
        help: 'Total consents granted',
        labelNames: ['consent_type'],
      }),
    },
    {
      provide: 'consent_withdrawn_total',
      useFactory: () => new Counter({
        name: 'consent_withdrawn_total',
        help: 'Total consents withdrawn',
        labelNames: ['consent_type'],
      }),
    },
    {
      provide: 'consent_requested_total',
      useFactory: () => new Counter({
        name: 'consent_requested_total',
        help: 'Total consents requested',
        labelNames: ['consent_type'],
      }),
    },
    {
      provide: 'privacy_request_processing_duration_seconds',
      useFactory: () => new Histogram({
        name: 'privacy_request_processing_duration_seconds',
        help: 'Privacy request processing duration in seconds',
        labelNames: ['type'],
        buckets: [60, 300, 900, 3600, 7200, 14400, 86400], // 1m, 5m, 15m, 1h, 2h, 4h, 24h
      }),
    },
    {
      provide: 'compliance_score_by_category',
      useFactory: () => new Gauge({
        name: 'compliance_score_by_category',
        help: 'Compliance score by category (0-100)',
        labelNames: ['category'],
      }),
    },
    {
      provide: 'privacy_incidents_total',
      useFactory: () => new Counter({
        name: 'privacy_incidents_total',
        help: 'Total privacy incidents',
        labelNames: ['severity', 'type'],
      }),
    },
    // WebSocket Metrics
    {
      provide: 'websocket_connections_active',
      useFactory: () => new Gauge({
        name: 'websocket_connections_active',
        help: 'Number of active WebSocket connections',
      }),
    },
    {
      provide: 'websocket_messages_total',
      useFactory: () => new Counter({
        name: 'websocket_messages_total',
        help: 'Total WebSocket messages',
        labelNames: ['direction', 'type'],
      }),
    },
    // AMNA Specific Metrics
    {
      provide: 'amna_messages_processed_total',
      useFactory: () => new Counter({
        name: 'amna_messages_processed_total',
        help: 'Total AMNA messages processed',
        labelNames: ['source', 'type'],
      }),
    },
  ],
  controllers: [MonitoringController],
  exports: [BusinessMetricsService, AlertingService, MetricsExportService],
})
export class MonitoringModule {}