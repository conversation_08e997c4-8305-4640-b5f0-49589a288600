import { Injectable } from '@nestjs/common';
import { FeatureFlagService } from '../feature-flags/feature-flag.service';
import { RequireFeatureFlag, IfFeatureEnabled } from '../feature-flags/decorators/feature-flag.decorator';
import { AMNAIntegrationService } from '../services/core/amna-integration.service';
import { EventBusService } from '../services/core/event-bus.service';

/**
 * Example service showing feature flag usage
 */
@Injectable()
export class IntegrationServiceWithFeatureFlags {
  constructor(
    private readonly featureFlagService: FeatureFlagService,
    private readonly amnaIntegrationService: AMNAIntegrationService,
    private readonly eventBusService: EventBusService,
  ) {}

  /**
   * Process activity with feature flags
   */
  async processActivity(activityData: any) {
    const user = { id: activityData.userId };
    
    // Check if the source application integration is enabled
    const sourceEnabled = await this.featureFlagService.isEnabled(
      `integration.${activityData.source}.enabled`,
      user,
      true,
    );

    if (!sourceEnabled) {
      throw new Error(`${activityData.source} integration is disabled`);
    }

    // Get integration feature flags
    const flags = await this.featureFlagService.getIntegrationFlags(user);

    // Process based on flags
    if (flags.enableRealtimeSync) {
      await this.processRealtimeSync(activityData);
    } else {
      await this.processBatchSync(activityData);
    }

    // AI insights if enabled
    if (flags.enableAIInsights) {
      await this.generateAIInsights(activityData);
    }

    // Check rollout percentage
    const rolloutPercentage = flags.rolloutPercentages[activityData.source];
    const userBucket = this.getUserBucket(user.id);
    
    if (userBucket < rolloutPercentage) {
      // User is in rollout group
      await this.processAdvancedFeatures(activityData);
    }

    return { processed: true };
  }

  /**
   * Method that requires feature flag to be enabled
   */
  @RequireFeatureFlag('integration.advanced-analytics.enabled')
  async performAdvancedAnalytics(data: any) {
    // This method will only execute if the feature flag is enabled
    console.log('Performing advanced analytics...');
    return { analytics: 'advanced' };
  }

  /**
   * Method that conditionally executes based on feature flag
   */
  @IfFeatureEnabled('integration.experimental.enabled')
  async experimentalFeature(data: any) {
    // This method executes only if flag is enabled, otherwise returns null
    console.log('Running experimental feature...');
    return { experimental: true };
  }

  /**
   * Sync with gradual rollout
   */
  async syncWithRollout(userId: string, applications: string[]) {
    const user = { id: userId };
    const flags = await this.featureFlagService.getIntegrationFlags(user);
    
    const enabledApplications = applications.filter(app => {
      // Check if app is enabled via feature flag
      const appKey = app.replace(/-/g, '');
      return flags[`enable${appKey.charAt(0).toUpperCase() + appKey.slice(1)}Integration`];
    });

    // Sync only enabled applications
    for (const app of enabledApplications) {
      await this.syncApplication(app, userId);
    }

    return { synced: enabledApplications };
  }

  /**
   * Dynamic configuration based on feature flags
   */
  async getDynamicConfig(userId: string) {
    const user = { id: userId };
    
    // Get numeric configuration values
    const batchSize = await this.featureFlagService.getValue(
      'integration.sync.batch-size',
      user,
      100,
    );

    const timeout = await this.featureFlagService.getValue(
      'integration.sync.timeout',
      user,
      30000,
    );

    const cacheConfig = await this.featureFlagService.getValue(
      'integration.cache.config',
      user,
      { enabled: true, ttl: 300 },
    );

    return {
      batchSize,
      timeout,
      cache: cacheConfig,
    };
  }

  /**
   * A/B testing with feature flags
   */
  async performABTest(userId: string, operation: string) {
    const user = { id: userId };
    
    // Get variation for A/B test
    const variation = await this.featureFlagService.evaluate(
      `integration.ab-test.${operation}`,
      user,
      'control',
    );

    switch (variation.value) {
      case 'control':
        return this.performStandardOperation(operation);
      case 'variant-a':
        return this.performVariantAOperation(operation);
      case 'variant-b':
        return this.performVariantBOperation(operation);
      default:
        return this.performStandardOperation(operation);
    }
  }

  /**
   * Circuit breaker with feature flag control
   */
  async callExternalService(service: string, data: any) {
    const circuitBreakerEnabled = await this.featureFlagService.isEnabled(
      'integration.circuit-breaker.enabled',
      undefined,
      true,
    );

    if (circuitBreakerEnabled) {
      // Use circuit breaker pattern
      return this.callWithCircuitBreaker(service, data);
    } else {
      // Direct call without circuit breaker
      return this.directServiceCall(service, data);
    }
  }

  // Helper methods
  private async processRealtimeSync(data: any) {
    console.log('Processing real-time sync...');
    await this.eventBusService.emit('activity.realtime', data);
  }

  private async processBatchSync(data: any) {
    console.log('Processing batch sync...');
    await this.eventBusService.emit('activity.batch', data);
  }

  private async generateAIInsights(data: any) {
    console.log('Generating AI insights...');
    return this.amnaIntegrationService.generateInsights(data);
  }

  private async processAdvancedFeatures(data: any) {
    console.log('Processing advanced features...');
  }

  private async syncApplication(app: string, userId: string) {
    console.log(`Syncing ${app} for user ${userId}`);
  }

  private getUserBucket(userId: string): number {
    // Simple hash for bucketing
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = ((hash << 5) - hash) + userId.charCodeAt(i);
    }
    return Math.abs(hash) % 100;
  }

  private async performStandardOperation(operation: string) {
    return { type: 'standard', operation };
  }

  private async performVariantAOperation(operation: string) {
    return { type: 'variant-a', operation };
  }

  private async performVariantBOperation(operation: string) {
    return { type: 'variant-b', operation };
  }

  private async callWithCircuitBreaker(service: string, data: any) {
    console.log('Calling with circuit breaker...');
    return { service, data, circuitBreaker: true };
  }

  private async directServiceCall(service: string, data: any) {
    console.log('Direct service call...');
    return { service, data, circuitBreaker: false };
  }
}

/**
 * Example controller with feature flags
 */
import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FeatureFlag, FeatureFlagValue } from '../feature-flags/decorators/feature-flag.decorator';

@Controller('api/integration/example')
@UseGuards(AuthGuard('jwt'))
export class FeatureFlagExampleController {
  constructor(
    private readonly integrationService: IntegrationServiceWithFeatureFlags,
  ) {}

  @Post('process')
  async processWithFlags(
    @Body() data: any,
    @Request() req: any,
    @FeatureFlag('integration.new-process.enabled') newProcessEnabled: boolean,
    @FeatureFlagValue({ 
      key: 'integration.process.config',
      defaultValue: { mode: 'standard' },
    }) processConfig: any,
  ) {
    if (newProcessEnabled) {
      // Use new process
      console.log('Using new process with config:', processConfig);
      return { process: 'new', config: processConfig };
    } else {
      // Use legacy process
      console.log('Using legacy process');
      return { process: 'legacy' };
    }
  }
}