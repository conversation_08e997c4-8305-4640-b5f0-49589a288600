import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { Redis } from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { ConfigService } from '@nestjs/config';

/**
 * Global rate limiting middleware for integration endpoints
 */
@Injectable()
export class IntegrationRateLimitMiddleware implements NestMiddleware {
  private readonly globalLimits = {
    perIp: {
      windowMs: 60000, // 1 minute
      maxRequests: 1000,
    },
    perUser: {
      windowMs: 60000,
      maxRequests: 2000,
    },
    perApplication: {
      windowMs: 60000,
      maxRequests: 5000,
    },
  };

  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly configService: ConfigService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Check global rate limits
      const checks = await Promise.all([
        this.checkIpLimit(req),
        this.checkUserLimit(req),
        this.checkApplicationLimit(req),
      ]);

      const failed = checks.find(check => !check.allowed);
      
      if (failed) {
        res.status(429).json({
          statusCode: 429,
          message: 'Global rate limit exceeded',
          error: 'Too Many Requests',
          retryAfter: failed.retryAfter,
          limit: failed.limit,
          remaining: 0,
        });
        return;
      }

      // Set rate limit headers
      const mostRestrictive = checks.reduce((prev, curr) => 
        curr.remaining < prev.remaining ? curr : prev
      );

      res.setHeader('X-Global-RateLimit-Limit', mostRestrictive.limit);
      res.setHeader('X-Global-RateLimit-Remaining', mostRestrictive.remaining);
      res.setHeader('X-Global-RateLimit-Reset', new Date(mostRestrictive.reset).toISOString());

      next();
    } catch (error) {
      // Don't block requests on rate limit errors
      console.error('Rate limit middleware error:', error);
      next();
    }
  }

  private async checkIpLimit(req: Request): Promise<RateLimitResult> {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const key = `global-rate-limit:ip:${ip}`;
    
    return this.checkLimit(key, this.globalLimits.perIp);
  }

  private async checkUserLimit(req: Request): Promise<RateLimitResult> {
    const userId = (req as any).user?.id;
    if (!userId) {
      return { allowed: true, limit: 0, remaining: 0, reset: 0, retryAfter: 0 };
    }

    const key = `global-rate-limit:user:${userId}`;
    return this.checkLimit(key, this.globalLimits.perUser);
  }

  private async checkApplicationLimit(req: Request): Promise<RateLimitResult> {
    const source = req.body?.source || req.headers['x-source-application'];
    if (!source) {
      return { allowed: true, limit: 0, remaining: 0, reset: 0, retryAfter: 0 };
    }

    const key = `global-rate-limit:app:${source}`;
    return this.checkLimit(key, this.globalLimits.perApplication);
  }

  private async checkLimit(
    key: string,
    limit: { windowMs: number; maxRequests: number },
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - limit.windowMs;

    // Use Redis sorted set for sliding window
    await this.redis.zremrangebyscore(key, '-inf', windowStart);
    
    const count = await this.redis.zcard(key);
    
    if (count >= limit.maxRequests) {
      const oldestEntry = await this.redis.zrange(key, 0, 0, 'WITHSCORES');
      const reset = oldestEntry.length > 1
        ? parseInt(oldestEntry[1]) + limit.windowMs
        : now + limit.windowMs;

      return {
        allowed: false,
        limit: limit.maxRequests,
        remaining: 0,
        reset,
        retryAfter: Math.ceil((reset - now) / 1000),
      };
    }

    // Add current request
    await this.redis.zadd(key, now, `${now}:${Math.random()}`);
    await this.redis.expire(key, Math.ceil(limit.windowMs / 1000));

    return {
      allowed: true,
      limit: limit.maxRequests,
      remaining: limit.maxRequests - count - 1,
      reset: now + limit.windowMs,
      retryAfter: 0,
    };
  }
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  reset: number;
  retryAfter: number;
}

/**
 * Adaptive rate limiting middleware that adjusts limits based on system load
 */
@Injectable()
export class AdaptiveRateLimitMiddleware implements NestMiddleware {
  private loadMultiplier = 1;
  private lastLoadCheck = 0;
  private readonly loadCheckInterval = 30000; // 30 seconds

  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly configService: ConfigService,
  ) {
    // Start load monitoring
    this.startLoadMonitoring();
  }

  async use(req: Request, res: Response, next: NextFunction) {
    // Update load multiplier if needed
    await this.updateLoadMultiplier();

    // Apply adaptive rate limiting
    const baseLimit = 100;
    const adaptiveLimit = Math.floor(baseLimit * this.loadMultiplier);
    
    const key = `adaptive:${req.ip}:${req.path}`;
    const count = await this.redis.incr(key);
    
    if (count === 1) {
      await this.redis.expire(key, 60); // 1 minute window
    }

    if (count > adaptiveLimit) {
      res.status(429).json({
        statusCode: 429,
        message: 'Adaptive rate limit exceeded',
        error: 'System under high load',
        currentLoad: Math.round((1 - this.loadMultiplier) * 100),
        adaptiveLimit,
      });
      return;
    }

    res.setHeader('X-Adaptive-Limit', adaptiveLimit);
    res.setHeader('X-Adaptive-Remaining', adaptiveLimit - count);
    res.setHeader('X-System-Load', Math.round((1 - this.loadMultiplier) * 100));

    next();
  }

  private async updateLoadMultiplier() {
    const now = Date.now();
    if (now - this.lastLoadCheck < this.loadCheckInterval) {
      return;
    }

    this.lastLoadCheck = now;

    // Get system metrics
    const metrics = await this.getSystemMetrics();
    
    // Calculate load multiplier based on metrics
    const cpuLoad = metrics.cpu;
    const memoryLoad = metrics.memory;
    const queueDepth = metrics.queueDepth;

    // Adaptive algorithm
    if (cpuLoad > 80 || memoryLoad > 85 || queueDepth > 1000) {
      this.loadMultiplier = 0.5; // Reduce to 50%
    } else if (cpuLoad > 60 || memoryLoad > 70 || queueDepth > 500) {
      this.loadMultiplier = 0.75; // Reduce to 75%
    } else if (cpuLoad < 40 && memoryLoad < 50 && queueDepth < 100) {
      this.loadMultiplier = 1.25; // Increase to 125%
    } else {
      this.loadMultiplier = 1; // Normal
    }
  }

  private async getSystemMetrics() {
    // Get metrics from Redis or monitoring system
    const metrics = await this.redis.hgetall('system:metrics');
    
    return {
      cpu: parseFloat(metrics.cpu || '0'),
      memory: parseFloat(metrics.memory || '0'),
      queueDepth: parseInt(metrics.queueDepth || '0'),
    };
  }

  private startLoadMonitoring() {
    // Simulate load monitoring (in production, use actual metrics)
    setInterval(async () => {
      const cpu = Math.random() * 100;
      const memory = Math.random() * 100;
      const queueDepth = Math.floor(Math.random() * 2000);

      await this.redis.hmset('system:metrics', {
        cpu: cpu.toString(),
        memory: memory.toString(),
        queueDepth: queueDepth.toString(),
        timestamp: Date.now().toString(),
      });
    }, 10000);
  }
}