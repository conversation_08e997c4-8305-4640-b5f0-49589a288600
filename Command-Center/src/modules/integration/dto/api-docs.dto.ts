import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsBoolean, IsNumber, IsEnum, IsOptional, IsArray, ValidateNested, IsUUID, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

// Enums
export enum IntegrationSource {
  E_CONNECT = 'e-connect',
  LIGHTHOUSE = 'lighthouse',
  TRAINING = 'training',
  VENDORS = 'vendors',
  WINS = 'wins-of-week',
  AMNA = 'amna',
}

export enum ActivityType {
  EMAIL_SENT = 'email.sent',
  EMAIL_RECEIVED = 'email.received',
  RESEARCH_COMPLETED = 'research.completed',
  INSIGHT_GENERATED = 'insight.generated',
  SKILL_GAP_IDENTIFIED = 'skill.gap.identified',
  TRAINING_COMPLETED = 'training.completed',
  VENDOR_MATCHED = 'vendor.matched',
  CONTRACT_SIGNED = 'contract.signed',
  ACHIEVEMENT_UNLOCKED = 'achievement.unlocked',
}

export enum SyncFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
}

export enum IntelligenceLevel {
  BASIC = 'basic',
  STANDARD = 'standard',
  ADVANCED = 'advanced',
}

// Request DTOs
export class CreateActivityDto {
  @ApiProperty({ 
    description: 'User ID associated with the activity',
    example: 'user-123' 
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({ 
    enum: IntegrationSource,
    description: 'Source application of the activity',
    example: IntegrationSource.E_CONNECT 
  })
  @IsEnum(IntegrationSource)
  source: IntegrationSource;

  @ApiProperty({ 
    enum: ActivityType,
    description: 'Type of activity',
    example: ActivityType.EMAIL_SENT 
  })
  @IsEnum(ActivityType)
  type: ActivityType;

  @ApiProperty({ 
    description: 'Activity data payload',
    example: { to: '<EMAIL>', subject: 'Project Update' }
  })
  data: Record<string, any>;

  @ApiPropertyOptional({ 
    description: 'Timestamp of the activity',
    example: '2024-01-01T00:00:00Z' 
  })
  @IsOptional()
  @IsDateString()
  timestamp?: string;

  @ApiPropertyOptional({ 
    description: 'Destination application for cross-app activities',
    enum: IntegrationSource,
    example: IntegrationSource.AMNA 
  })
  @IsOptional()
  @IsEnum(IntegrationSource)
  destination?: IntegrationSource;
}

export class UpdatePreferencesDto {
  @ApiPropertyOptional({ 
    description: 'Enable E-Connect integration',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  e_connect_integration?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable Lighthouse integration',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  lighthouse_integration?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable Training integration',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  training_integration?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable Vendors integration',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  vendor_integration?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable Wins tracking',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  wins_tracking?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable real-time synchronization',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  real_time_sync?: boolean;

  @ApiPropertyOptional({ 
    enum: SyncFrequency,
    description: 'Synchronization frequency',
    example: SyncFrequency.IMMEDIATE 
  })
  @IsOptional()
  @IsEnum(SyncFrequency)
  sync_frequency?: SyncFrequency;

  @ApiPropertyOptional({ 
    enum: IntelligenceLevel,
    description: 'AMNA intelligence level',
    example: IntelligenceLevel.STANDARD 
  })
  @IsOptional()
  @IsEnum(IntelligenceLevel)
  intelligence_level?: IntelligenceLevel;

  @ApiPropertyOptional({ 
    description: 'Enable cross-application data sharing',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  cross_app_data_sharing?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable activity tracking',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  activity_tracking?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable AI analysis',
    example: true 
  })
  @IsOptional()
  @IsBoolean()
  ai_analysis?: boolean;
}

export class SyncRequestDto {
  @ApiPropertyOptional({ 
    description: 'Applications to sync',
    example: ['e-connect', 'lighthouse'],
    isArray: true,
    enum: IntegrationSource
  })
  @IsOptional()
  @IsArray()
  @IsEnum(IntegrationSource, { each: true })
  applications?: IntegrationSource[];

  @ApiPropertyOptional({ 
    description: 'Force sync even if recently synced',
    example: false 
  })
  @IsOptional()
  @IsBoolean()
  force?: boolean;
}

export class QueryActivitiesDto {
  @ApiPropertyOptional({ 
    description: 'Filter by source application',
    enum: IntegrationSource,
    example: IntegrationSource.E_CONNECT 
  })
  @IsOptional()
  @IsEnum(IntegrationSource)
  source?: IntegrationSource;

  @ApiPropertyOptional({ 
    description: 'Filter by activity type',
    enum: ActivityType,
    example: ActivityType.EMAIL_SENT 
  })
  @IsOptional()
  @IsEnum(ActivityType)
  type?: ActivityType;

  @ApiPropertyOptional({ 
    description: 'Start date for filtering',
    example: '2024-01-01T00:00:00Z' 
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'End date for filtering',
    example: '2024-12-31T23:59:59Z' 
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: 'Number of results to return',
    example: 50,
    minimum: 1,
    maximum: 1000 
  })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ 
    description: 'Offset for pagination',
    example: 0,
    minimum: 0 
  })
  @IsOptional()
  @IsNumber()
  offset?: number;
}

// Response DTOs
export class ActivityResponseDto {
  @ApiProperty({ 
    description: 'Activity ID',
    example: 'activity-123' 
  })
  id: string;

  @ApiProperty({ 
    description: 'User ID',
    example: 'user-123' 
  })
  userId: string;

  @ApiProperty({ 
    enum: IntegrationSource,
    description: 'Source application' 
  })
  source: IntegrationSource;

  @ApiProperty({ 
    enum: ActivityType,
    description: 'Activity type' 
  })
  type: ActivityType;

  @ApiProperty({ 
    description: 'Activity data' 
  })
  data: Record<string, any>;

  @ApiProperty({ 
    description: 'Processing status',
    example: false 
  })
  processed: boolean;

  @ApiProperty({ 
    description: 'Activity timestamp',
    example: '2024-01-01T00:00:00Z' 
  })
  timestamp: string;

  @ApiPropertyOptional({ 
    description: 'Processing result' 
  })
  processingResult?: Record<string, any>;

  @ApiPropertyOptional({ 
    description: 'AMNA insights' 
  })
  insights?: string[];

  @ApiPropertyOptional({ 
    description: 'Achievement level',
    example: 'gold' 
  })
  achievementLevel?: string;
}

export class PreferencesResponseDto extends UpdatePreferencesDto {
  @ApiProperty({ 
    description: 'User ID',
    example: 'user-123' 
  })
  id: string;

  @ApiProperty({ 
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00Z' 
  })
  created_at: string;

  @ApiProperty({ 
    description: 'Last update timestamp',
    example: '2024-01-01T00:00:00Z' 
  })
  updated_at: string;
}

export class SyncStatusResponseDto {
  @ApiProperty({ 
    description: 'User ID',
    example: 'user-123' 
  })
  userId: string;

  @ApiProperty({ 
    description: 'Sync status by application',
    example: {
      'e-connect': { lastSync: '2024-01-01T00:00:00Z', status: 'success' },
      'lighthouse': { lastSync: '2024-01-01T00:00:00Z', status: 'success' }
    }
  })
  applications: Record<string, {
    lastSync: string;
    status: 'success' | 'failed' | 'pending';
    error?: string;
  }>;

  @ApiProperty({ 
    description: 'Overall sync status',
    example: 'completed' 
  })
  overallStatus: string;

  @ApiProperty({ 
    description: 'Next scheduled sync',
    example: '2024-01-01T01:00:00Z' 
  })
  nextSync?: string;
}

export class FederatedDataResponseDto {
  @ApiProperty({ 
    description: 'User ID',
    example: 'user-123' 
  })
  userId: string;

  @ApiProperty({ 
    description: 'Federated data from all applications',
    example: {
      'e-connect': { emails: 150, automations: 5 },
      'lighthouse': { researches: 12, insights: 45 },
      'training': { assessments: 3, skills: ['AWS', 'Kubernetes'] },
      'vendors': { active: 5, contracts: 8 }
    }
  })
  data: Record<string, any>;

  @ApiProperty({ 
    description: 'Data freshness by application',
    example: {
      'e-connect': '2024-01-01T00:00:00Z',
      'lighthouse': '2024-01-01T00:00:00Z'
    }
  })
  dataFreshness: Record<string, string>;

  @ApiProperty({ 
    description: 'Cross-application insights',
    example: [
      'High email engagement with training vendors',
      'Research insights align with skill gaps'
    ]
  })
  insights: string[];

  @ApiProperty({ 
    description: 'Recommended actions',
    example: [
      'Schedule training for identified skill gaps',
      'Follow up on vendor proposals'
    ]
  })
  recommendations: string[];
}

export class HealthStatusResponseDto {
  @ApiProperty({ 
    description: 'Overall system health',
    example: 'healthy' 
  })
  overall: 'healthy' | 'degraded' | 'unhealthy';

  @ApiProperty({ 
    description: 'Individual service health',
    example: {
      'e-connect': { status: 'healthy', latency: 45 },
      'lighthouse': { status: 'healthy', latency: 62 },
      'database': { status: 'healthy', connections: 15 },
      'redis': { status: 'healthy', memory: '256MB' }
    }
  })
  services: Record<string, {
    status: string;
    latency?: number;
    error?: string;
    [key: string]: any;
  }>;

  @ApiProperty({ 
    description: 'System metrics',
    example: {
      uptime: 86400,
      requestsPerMinute: 150,
      activeUsers: 45,
      queueDepth: 12
    }
  })
  metrics: {
    uptime: number;
    requestsPerMinute: number;
    activeUsers: number;
    queueDepth: number;
  };

  @ApiProperty({ 
    description: 'Timestamp of health check',
    example: '2024-01-01T00:00:00Z' 
  })
  timestamp: string;
}

// Error Response DTOs
export class ErrorResponseDto {
  @ApiProperty({ 
    description: 'HTTP status code',
    example: 400 
  })
  statusCode: number;

  @ApiProperty({ 
    description: 'Error message',
    example: 'Bad Request' 
  })
  message: string;

  @ApiProperty({ 
    description: 'Error details',
    example: 'Invalid user ID format' 
  })
  error: string;

  @ApiPropertyOptional({ 
    description: 'Timestamp',
    example: '2024-01-01T00:00:00Z' 
  })
  timestamp?: string;

  @ApiPropertyOptional({ 
    description: 'Request path',
    example: '/api/integration/activity' 
  })
  path?: string;
}

export class RateLimitErrorDto extends ErrorResponseDto {
  @ApiProperty({ 
    description: 'Retry after (seconds)',
    example: 60 
  })
  retryAfter: number;

  @ApiProperty({ 
    description: 'Rate limit',
    example: 100 
  })
  limit: number;

  @ApiProperty({ 
    description: 'Remaining requests',
    example: 0 
  })
  remainingRequests: number;

  @ApiProperty({ 
    description: 'Reset time',
    example: '2024-01-01T00:01:00Z' 
  })
  resetTime: string;
}