{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "../../../..", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/modules/integration/**/*.(t|j)s", "!src/modules/integration/**/*.spec.ts", "!src/modules/integration/**/*.interface.ts"], "coverageDirectory": "../coverage-e2e", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/src/modules/integration/test/e2e/setup.ts"]}