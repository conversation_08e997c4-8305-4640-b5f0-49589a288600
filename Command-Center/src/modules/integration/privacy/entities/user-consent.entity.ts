import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('user_consents')
@Index(['userId', 'type', 'status'])
export class UserConsent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  userId: string;

  @Column()
  type: string;

  @Column()
  status: string;

  @Column({ type: 'timestamp', nullable: true })
  grantedAt: Date | null;

  @Column({ nullable: true })
  withdrawnBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  withdrawnAt: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date | null;

  @Column()
  legalBasis: string;

  @Column({ type: 'text' })
  purpose: string;

  @Column()
  version: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}