import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PrivacyService, PrivacyRequestType } from '../services/privacy.service';
import { PrivacyGuard } from '../guards/privacy.guard';
import { RateLimitGuard } from '../../guards/enhanced-rate-limit.guard';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    roles: string[];
  };
}

@ApiTags('Privacy')
@Controller('api/privacy')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class PrivacyController {
  constructor(private readonly privacyService: PrivacyService) {}

  /**
   * Get user's personal data (GDPR Article 15)
   */
  @Get('my-data')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Get my personal data' })
  @ApiResponse({ status: 200, description: 'Personal data retrieved successfully' })
  async getMyData(@Request() req: AuthenticatedRequest) {
    return this.privacyService.getUserData(req.user.id, req.user.id);
  }

  /**
   * Export user data (GDPR Article 20)
   */
  @Post('export')
  @UseGuards(PrivacyGuard, RateLimitGuard)
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Request data export' })
  @ApiResponse({ status: 202, description: 'Export request accepted' })
  async exportData(
    @Request() req: AuthenticatedRequest,
    @Body() body: { format?: 'json' | 'csv' | 'xml' },
  ) {
    return this.privacyService.exportUserData(
      req.user.id,
      body.format || 'json',
      req.user.id,
    );
  }

  /**
   * Delete user data (GDPR Article 17)
   */
  @Delete('my-data')
  @UseGuards(PrivacyGuard, RateLimitGuard)
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Request data deletion' })
  @ApiResponse({ status: 202, description: 'Deletion request accepted' })
  async deleteMyData(
    @Request() req: AuthenticatedRequest,
    @Body() body: {
      reason: string;
      hardDelete?: boolean;
      anonymizeInstead?: boolean;
    },
  ) {
    return this.privacyService.deleteUserData(
      req.user.id,
      body.reason,
      req.user.id,
      {
        hardDelete: body.hardDelete,
        anonymizeInstead: body.anonymizeInstead,
      },
    );
  }

  /**
   * Update user data (GDPR Article 16)
   */
  @Put('my-data')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Update my personal data' })
  @ApiResponse({ status: 200, description: 'Data updated successfully' })
  async updateMyData(
    @Request() req: AuthenticatedRequest,
    @Body() updates: Record<string, any>,
  ) {
    await this.privacyService.updateUserData(req.user.id, updates, req.user.id);
    return { message: 'Data updated successfully' };
  }

  /**
   * Restrict data processing (GDPR Article 18)
   */
  @Post('restrict-processing')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Restrict data processing' })
  @ApiResponse({ status: 200, description: 'Processing restricted' })
  async restrictProcessing(
    @Request() req: AuthenticatedRequest,
    @Body() body: { categories: string[]; reason: string },
  ) {
    await this.privacyService.restrictProcessing(
      req.user.id,
      body.categories,
      body.reason,
      req.user.id,
    );
    return { message: 'Processing restricted successfully' };
  }

  /**
   * Get privacy request status
   */
  @Get('requests/:requestId')
  @ApiOperation({ summary: 'Get privacy request status' })
  @ApiResponse({ status: 200, description: 'Request status retrieved' })
  async getRequestStatus(@Param('requestId') requestId: string) {
    return this.privacyService.getRequestStatus(requestId);
  }

  /**
   * Get all privacy requests for user
   */
  @Get('requests')
  @ApiOperation({ summary: 'Get my privacy requests' })
  @ApiResponse({ status: 200, description: 'Privacy requests retrieved' })
  async getMyRequests(@Request() req: AuthenticatedRequest) {
    return this.privacyService.getUserRequests(req.user.id);
  }

  /**
   * Cancel privacy request
   */
  @Post('requests/:requestId/cancel')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Cancel privacy request' })
  @ApiResponse({ status: 200, description: 'Request cancelled' })
  async cancelRequest(
    @Request() req: AuthenticatedRequest,
    @Param('requestId') requestId: string,
    @Body() body: { reason: string },
  ) {
    await this.privacyService.cancelRequest(
      requestId,
      body.reason,
      req.user.id,
    );
    return { message: 'Request cancelled successfully' };
  }

  /**
   * Anonymize user data
   */
  @Post('anonymize')
  @UseGuards(PrivacyGuard, RateLimitGuard)
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Anonymize my data' })
  @ApiResponse({ status: 202, description: 'Anonymization request accepted' })
  async anonymizeData(@Request() req: AuthenticatedRequest) {
    return this.privacyService.anonymizeUserData(req.user.id, req.user.id);
  }

  /**
   * Download exported data
   */
  @Get('exports/:exportId/download')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Download exported data' })
  @ApiResponse({ status: 200, description: 'Export file' })
  async downloadExport(
    @Request() req: AuthenticatedRequest,
    @Param('exportId') exportId: string,
  ) {
    // This would typically return a file download
    // Implementation depends on storage service
    return {
      message: 'Download endpoint - implement with actual file service',
      exportId,
    };
  }

  /**
   * Admin: Get user data (with proper authorization)
   */
  @Get('users/:userId/data')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Get user data (admin)' })
  @ApiResponse({ status: 200, description: 'User data retrieved' })
  async getUserData(
    @Request() req: AuthenticatedRequest,
    @Param('userId') userId: string,
  ) {
    // Check admin permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('dpo')) {
      throw new Error('Insufficient permissions');
    }

    return this.privacyService.getUserData(userId, req.user.id);
  }

  /**
   * Admin: Process privacy request
   */
  @Post('admin/requests')
  @UseGuards(PrivacyGuard)
  @ApiOperation({ summary: 'Create privacy request (admin)' })
  @ApiResponse({ status: 201, description: 'Request created' })
  async createPrivacyRequest(
    @Request() req: AuthenticatedRequest,
    @Body() body: {
      userId: string;
      type: PrivacyRequestType;
      details: any;
    },
  ) {
    // Check DPO permissions
    if (!req.user.roles.includes('dpo')) {
      throw new Error('Only Data Protection Officers can create privacy requests');
    }

    return this.privacyService.createPrivacyRequest(
      body.userId,
      body.type,
      body.details,
      req.user.id,
    );
  }
}