import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { ConsentManagementService, ConsentType } from '../services/consent-management.service';
import { ConsentGuard } from '../guards/consent.guard';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
  };
}

@ApiTags('Consent')
@Controller('api/consent')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class ConsentController {
  constructor(private readonly consentService: ConsentManagementService) {}

  /**
   * Get current consent status
   */
  @Get('status')
  @ApiOperation({ summary: 'Get my consent status' })
  @ApiResponse({ status: 200, description: 'Consent status retrieved' })
  async getConsentStatus(
    @Request() req: AuthenticatedRequest,
    @Query('type') type?: ConsentType,
  ) {
    return this.consentService.getConsentStatus(req.user.id, type);
  }

  /**
   * Grant consent
   */
  @Post('grant')
  @ApiOperation({ summary: 'Grant consent' })
  @ApiResponse({ status: 201, description: 'Consent granted' })
  async grantConsent(
    @Request() req: AuthenticatedRequest,
    @Body() body: {
      type: ConsentType;
      metadata?: any;
    },
  ) {
    return this.consentService.recordConsent(
      req.user.id,
      body.type,
      true,
      {
        ...body.metadata,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
      },
    );
  }

  /**
   * Withdraw consent
   */
  @Post('withdraw')
  @ApiOperation({ summary: 'Withdraw consent' })
  @ApiResponse({ status: 200, description: 'Consent withdrawn' })
  async withdrawConsent(
    @Request() req: AuthenticatedRequest,
    @Body() body: { types: ConsentType[] },
  ) {
    await this.consentService.withdrawConsent(
      req.user.id,
      body.types,
      req.user.id,
    );
    return { message: 'Consent withdrawn successfully' };
  }

  /**
   * Get consent preferences
   */
  @Get('preferences')
  @ApiOperation({ summary: 'Get my consent preferences' })
  @ApiResponse({ status: 200, description: 'Preferences retrieved' })
  async getPreferences(@Request() req: AuthenticatedRequest) {
    const prefs = await this.consentService.getUserConsentPreferences(req.user.id);
    return {
      userId: prefs.userId,
      preferences: Object.fromEntries(prefs.preferences),
      lastUpdated: prefs.lastUpdated,
    };
  }

  /**
   * Update consent preferences
   */
  @Put('preferences')
  @ApiOperation({ summary: 'Update consent preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated' })
  async updatePreferences(
    @Request() req: AuthenticatedRequest,
    @Body() body: { preferences: Record<ConsentType, boolean> },
  ) {
    const preferencesMap = new Map(Object.entries(body.preferences)) as Map<ConsentType, boolean>;
    
    await this.consentService.updateConsentPreferences(
      req.user.id,
      preferencesMap,
      {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        source: 'preferences_center',
      },
    );

    return { message: 'Preferences updated successfully' };
  }

  /**
   * Get consent history
   */
  @Get('history')
  @ApiOperation({ summary: 'Get my consent history' })
  @ApiResponse({ status: 200, description: 'History retrieved' })
  async getHistory(
    @Request() req: AuthenticatedRequest,
    @Query('type') type?: ConsentType,
    @Query('limit') limit?: number,
  ) {
    return this.consentService.getConsentHistory(
      req.user.id,
      type,
      limit || 50,
    );
  }

  /**
   * Check consent requirements
   */
  @Post('check-requirements')
  @UseGuards(ConsentGuard)
  @ApiOperation({ summary: 'Check consent requirements for operation' })
  @ApiResponse({ status: 200, description: 'Requirements checked' })
  async checkRequirements(
    @Request() req: AuthenticatedRequest,
    @Body() body: { requiredConsents: ConsentType[] },
  ) {
    return this.consentService.checkConsentRequirements(
      req.user.id,
      body.requiredConsents,
    );
  }

  /**
   * Export consent records
   */
  @Get('export')
  @ApiOperation({ summary: 'Export my consent records' })
  @ApiResponse({ status: 200, description: 'Consent records exported' })
  async exportConsents(@Request() req: AuthenticatedRequest) {
    return this.consentService.exportConsentRecords(req.user.id);
  }

  /**
   * Get consent statistics (admin only)
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get consent statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getStatistics(@Request() req: AuthenticatedRequest) {
    // Check admin permissions
    if (!req.user.roles?.includes('admin') && !req.user.roles?.includes('dpo')) {
      throw new Error('Insufficient permissions');
    }

    const stats = await this.consentService.getConsentStatistics();
    
    return {
      totalUsers: stats.totalUsers,
      consentRates: Object.fromEntries(stats.consentRates),
      withdrawalRates: Object.fromEntries(stats.withdrawalRates),
    };
  }
}