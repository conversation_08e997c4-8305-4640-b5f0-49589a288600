import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';
import * as forge from 'node-forge';

interface EncryptionResult {
  encrypted: string;
  iv: string;
  authTag: string;
  algorithm: string;
  keyId?: string;
}

interface KeyRotationInfo {
  currentKeyId: string;
  previousKeyId?: string;
  rotatedAt: Date;
  nextRotation: Date;
}

interface FieldEncryptionConfig {
  fields: string[];
  algorithm: 'aes-256-gcm' | 'aes-256-cbc' | 'rsa';
  keyType: 'master' | 'field-specific' | 'user-specific';
}

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly masterKey: Buffer;
  private readonly keyDerivationSalt: string;
  private readonly defaultAlgorithm = 'aes-256-gcm';
  private keyCache = new Map<string, Buffer>();
  private rsaKeyPair: { publicKey: string; privateKey: string };

  constructor(private readonly configService: ConfigService) {
    this.masterKey = this.initializeMasterKey();
    this.keyDerivationSalt = this.configService.get('KEY_DERIVATION_SALT', 'default-salt');
    this.rsaKeyPair = this.initializeRSAKeyPair();
  }

  /**
   * Encrypt data using AES-256-GCM
   */
  async encrypt(data: string, keyId?: string): Promise<EncryptionResult> {
    const key = keyId ? await this.getDerivedKey(keyId) : this.masterKey;
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.defaultAlgorithm, key, iv);

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      algorithm: this.defaultAlgorithm,
      keyId,
    };
  }

  /**
   * Decrypt data
   */
  async decrypt(encryptionResult: EncryptionResult): Promise<string> {
    const key = encryptionResult.keyId 
      ? await this.getDerivedKey(encryptionResult.keyId) 
      : this.masterKey;

    const decipher = crypto.createDecipheriv(
      encryptionResult.algorithm || this.defaultAlgorithm,
      key,
      Buffer.from(encryptionResult.iv, 'hex'),
    );

    if (encryptionResult.authTag) {
      decipher.setAuthTag(Buffer.from(encryptionResult.authTag, 'hex'));
    }

    let decrypted = decipher.update(encryptionResult.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Encrypt object fields
   */
  async encryptObjectFields(
    obj: any,
    config: FieldEncryptionConfig,
  ): Promise<{ encrypted: any; metadata: any }> {
    const encrypted = { ...obj };
    const metadata: any = {
      encryptedFields: [],
      algorithm: config.algorithm,
      timestamp: new Date(),
    };

    for (const field of config.fields) {
      const value = this.getNestedValue(obj, field);
      if (value !== undefined && value !== null) {
        const keyId = config.keyType === 'field-specific' 
          ? `field:${field}` 
          : config.keyType === 'user-specific' 
          ? `user:${obj.userId || obj.id}` 
          : undefined;

        const encryptionResult = await this.encrypt(String(value), keyId);
        
        this.setNestedValue(encrypted, field, {
          _encrypted: true,
          ...encryptionResult,
        });

        metadata.encryptedFields.push({
          field,
          keyType: config.keyType,
          keyId,
        });
      }
    }

    return { encrypted, metadata };
  }

  /**
   * Decrypt object fields
   */
  async decryptObjectFields(
    obj: any,
    metadata: any,
  ): Promise<any> {
    const decrypted = { ...obj };

    for (const fieldMeta of metadata.encryptedFields) {
      const encryptedValue = this.getNestedValue(obj, fieldMeta.field);
      
      if (encryptedValue && encryptedValue._encrypted) {
        try {
          const decryptedValue = await this.decrypt(encryptedValue);
          this.setNestedValue(decrypted, fieldMeta.field, decryptedValue);
        } catch (error) {
          this.logger.error(`Failed to decrypt field ${fieldMeta.field}`, error);
          // Keep encrypted value if decryption fails
        }
      }
    }

    return decrypted;
  }

  /**
   * Hash data (one-way)
   */
  async hash(data: string): Promise<string> {
    return bcrypt.hash(data, 12);
  }

  /**
   * Verify hashed data
   */
  async verifyHash(data: string, hash: string): Promise<boolean> {
    return bcrypt.compare(data, hash);
  }

  /**
   * Generate secure token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Encrypt with RSA (for small data or key exchange)
   */
  encryptRSA(data: string): string {
    const publicKey = forge.pki.publicKeyFromPem(this.rsaKeyPair.publicKey);
    const encrypted = publicKey.encrypt(data, 'RSA-OAEP', {
      md: forge.md.sha256.create(),
    });
    return forge.util.encode64(encrypted);
  }

  /**
   * Decrypt with RSA
   */
  decryptRSA(encryptedData: string): string {
    const privateKey = forge.pki.privateKeyFromPem(this.rsaKeyPair.privateKey);
    const encrypted = forge.util.decode64(encryptedData);
    return privateKey.decrypt(encrypted, 'RSA-OAEP', {
      md: forge.md.sha256.create(),
    });
  }

  /**
   * Encrypt large data with hybrid encryption
   */
  async encryptHybrid(data: string): Promise<{
    encryptedData: EncryptionResult;
    encryptedKey: string;
  }> {
    // Generate random AES key
    const aesKey = crypto.randomBytes(32);
    
    // Encrypt data with AES
    const tempKey = this.masterKey;
    this.masterKey = aesKey as any;
    const encryptedData = await this.encrypt(data);
    this.masterKey = tempKey;

    // Encrypt AES key with RSA
    const encryptedKey = this.encryptRSA(aesKey.toString('hex'));

    return { encryptedData, encryptedKey };
  }

  /**
   * Decrypt hybrid encrypted data
   */
  async decryptHybrid(
    encryptedData: EncryptionResult,
    encryptedKey: string,
  ): Promise<string> {
    // Decrypt AES key with RSA
    const aesKeyHex = this.decryptRSA(encryptedKey);
    const aesKey = Buffer.from(aesKeyHex, 'hex');

    // Decrypt data with AES
    const tempKey = this.masterKey;
    this.masterKey = aesKey as any;
    const decrypted = await this.decrypt(encryptedData);
    this.masterKey = tempKey;

    return decrypted;
  }

  /**
   * Create encrypted backup
   */
  async createEncryptedBackup(data: any): Promise<{
    backup: string;
    checksum: string;
    metadata: any;
  }> {
    const jsonData = JSON.stringify(data);
    const compressed = await this.compress(jsonData);
    const encrypted = await this.encrypt(compressed);
    
    const backup = JSON.stringify({
      version: '1.0',
      encrypted,
      timestamp: new Date(),
    });

    const checksum = crypto
      .createHash('sha256')
      .update(backup)
      .digest('hex');

    return {
      backup,
      checksum,
      metadata: {
        originalSize: jsonData.length,
        compressedSize: compressed.length,
        encryptedSize: encrypted.encrypted.length,
        algorithm: encrypted.algorithm,
      },
    };
  }

  /**
   * Restore from encrypted backup
   */
  async restoreEncryptedBackup(backup: string, checksum: string): Promise<any> {
    // Verify checksum
    const calculatedChecksum = crypto
      .createHash('sha256')
      .update(backup)
      .digest('hex');

    if (calculatedChecksum !== checksum) {
      throw new Error('Backup checksum verification failed');
    }

    const backupData = JSON.parse(backup);
    const decrypted = await this.decrypt(backupData.encrypted);
    const decompressed = await this.decompress(decrypted);
    
    return JSON.parse(decompressed);
  }

  /**
   * Key rotation
   */
  async rotateKeys(): Promise<KeyRotationInfo> {
    this.logger.log('Starting key rotation');

    const currentKeyId = this.generateKeyId();
    const previousKeyId = await this.getCurrentKeyId();

    // Generate new master key
    const newMasterKey = crypto.randomBytes(32);
    
    // Store new key securely
    await this.storeKey(currentKeyId, newMasterKey);

    // Re-encrypt existing data with new key
    await this.reencryptExistingData(previousKeyId, currentKeyId);

    // Update master key
    this.masterKey = newMasterKey as any;

    const rotationInfo: KeyRotationInfo = {
      currentKeyId,
      previousKeyId,
      rotatedAt: new Date(),
      nextRotation: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
    };

    this.logger.log('Key rotation completed', rotationInfo);

    return rotationInfo;
  }

  /**
   * Generate encryption key from password (PBKDF2)
   */
  async deriveKeyFromPassword(
    password: string,
    salt?: string,
  ): Promise<{ key: Buffer; salt: string }> {
    const usedSalt = salt || crypto.randomBytes(32).toString('hex');
    const key = crypto.pbkdf2Sync(password, usedSalt, 100000, 32, 'sha256');
    
    return { key, salt: usedSalt };
  }

  /**
   * Encrypt sensitive configuration
   */
  async encryptConfiguration(config: any): Promise<{
    encrypted: string;
    key: string;
  }> {
    const configString = JSON.stringify(config);
    const { encryptedData, encryptedKey } = await this.encryptHybrid(configString);
    
    return {
      encrypted: JSON.stringify(encryptedData),
      key: encryptedKey,
    };
  }

  /**
   * Decrypt sensitive configuration
   */
  async decryptConfiguration(encrypted: string, key: string): Promise<any> {
    const encryptedData = JSON.parse(encrypted);
    const decrypted = await this.decryptHybrid(encryptedData, key);
    
    return JSON.parse(decrypted);
  }

  // Private helper methods

  private initializeMasterKey(): Buffer {
    const keyHex = this.configService.get('MASTER_ENCRYPTION_KEY');
    
    if (!keyHex) {
      throw new Error('MASTER_ENCRYPTION_KEY not configured');
    }

    return Buffer.from(keyHex, 'hex');
  }

  private initializeRSAKeyPair(): { publicKey: string; privateKey: string } {
    const publicKey = this.configService.get('RSA_PUBLIC_KEY');
    const privateKey = this.configService.get('RSA_PRIVATE_KEY');

    if (!publicKey || !privateKey) {
      // Generate new key pair if not configured
      const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });
      
      return {
        publicKey: forge.pki.publicKeyToPem(keypair.publicKey),
        privateKey: forge.pki.privateKeyToPem(keypair.privateKey),
      };
    }

    return { publicKey, privateKey };
  }

  private async getDerivedKey(keyId: string): Promise<Buffer> {
    if (this.keyCache.has(keyId)) {
      return this.keyCache.get(keyId)!;
    }

    const derived = crypto.pbkdf2Sync(
      this.masterKey,
      `${this.keyDerivationSalt}:${keyId}`,
      10000,
      32,
      'sha256',
    );

    this.keyCache.set(keyId, derived);
    
    return derived;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key]) {
        current[key] = {};
      }
      return current[key];
    }, obj);

    target[lastKey] = value;
  }

  private async compress(data: string): Promise<string> {
    const zlib = await import('zlib');
    return new Promise((resolve, reject) => {
      zlib.gzip(data, (err, buffer) => {
        if (err) reject(err);
        else resolve(buffer.toString('base64'));
      });
    });
  }

  private async decompress(data: string): Promise<string> {
    const zlib = await import('zlib');
    return new Promise((resolve, reject) => {
      const buffer = Buffer.from(data, 'base64');
      zlib.gunzip(buffer, (err, result) => {
        if (err) reject(err);
        else resolve(result.toString('utf8'));
      });
    });
  }

  private generateKeyId(): string {
    return `key_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  private async getCurrentKeyId(): Promise<string | undefined> {
    // This would retrieve from secure storage
    return undefined;
  }

  private async storeKey(keyId: string, key: Buffer): Promise<void> {
    // This would store in secure key management system
    this.logger.debug(`Stored key: ${keyId}`);
  }

  private async reencryptExistingData(
    oldKeyId: string | undefined,
    newKeyId: string,
  ): Promise<void> {
    // This would re-encrypt all existing data with new key
    this.logger.debug(`Re-encrypting data from ${oldKeyId} to ${newKeyId}`);
  }
}