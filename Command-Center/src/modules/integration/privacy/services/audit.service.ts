import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AuditLog } from '../entities/audit-log.entity';
import * as crypto from 'crypto';

export enum AuditAction {
  // Privacy actions
  PRIVACY_REQUEST_CREATED = 'privacy_request_created',
  PRIVACY_REQUEST_COMPLETED = 'privacy_request_completed',
  PRIVACY_REQUEST_FAILED = 'privacy_request_failed',
  DATA_ACCESS = 'data_access',
  DATA_EXPORT = 'data_export',
  DATA_DELETION = 'data_deletion',
  DATA_ANONYMIZATION = 'data_anonymization',
  DATA_MODIFICATION = 'data_modification',
  
  // Consent actions
  CONSENT_GRANTED = 'consent_granted',
  CONSENT_WITHDRAWN = 'consent_withdrawn',
  CONSENT_UPDATED = 'consent_updated',
  
  // Security actions
  AUTHENTICATION_SUCCESS = 'authentication_success',
  AUTHENTICATION_FAILURE = 'authentication_failure',
  AUTHORIZATION_CHECK = 'authorization_check',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  
  // Administrative actions
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  PERMISSION_CHANGED = 'permission_changed',
  CONFIGURATION_CHANGED = 'configuration_changed',
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

interface AuditLogEntry {
  action: AuditAction;
  userId?: string;
  performedBy: string;
  entityType?: string;
  entityId?: string;
  changes?: any;
  metadata?: any;
  severity?: AuditSeverity;
  ipAddress?: string;
  userAgent?: string;
}

interface AuditSearchCriteria {
  userId?: string;
  performedBy?: string;
  action?: AuditAction;
  entityType?: string;
  entityId?: string;
  startDate?: Date;
  endDate?: Date;
  severity?: AuditSeverity;
}

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);
  private readonly encryptionKey: string;
  private readonly retentionDays: number;

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.encryptionKey = this.configService.get('AUDIT_ENCRYPTION_KEY', 'default-key');
    this.retentionDays = this.configService.get('AUDIT_RETENTION_DAYS', 365);
  }

  /**
   * Log a privacy request
   */
  async logPrivacyRequest(data: {
    requestId: string;
    userId: string;
    type: string;
    action: string;
    performedBy: string;
    metadata?: any;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.PRIVACY_REQUEST_CREATED,
      userId: data.userId,
      performedBy: data.performedBy,
      entityType: 'privacy_request',
      entityId: data.requestId,
      metadata: {
        requestType: data.type,
        ...data.metadata,
      },
      severity: AuditSeverity.HIGH,
    });
  }

  /**
   * Log data access
   */
  async logDataAccess(data: {
    userId: string;
    accessedBy: string;
    dataCategories: string[];
    purpose: string;
    metadata?: any;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.DATA_ACCESS,
      userId: data.userId,
      performedBy: data.accessedBy,
      metadata: {
        dataCategories: data.dataCategories,
        purpose: data.purpose,
        ...data.metadata,
      },
      severity: this.determineSeverity(data.dataCategories),
    });
  }

  /**
   * Log data export
   */
  async logDataExport(data: {
    userId: string;
    exportedBy: string;
    format: string;
    categories: string[];
    exportId: string;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.DATA_EXPORT,
      userId: data.userId,
      performedBy: data.exportedBy,
      entityType: 'data_export',
      entityId: data.exportId,
      metadata: {
        format: data.format,
        categories: data.categories,
        timestamp: new Date(),
      },
      severity: AuditSeverity.HIGH,
    });
  }

  /**
   * Log data deletion
   */
  async logDataDeletion(data: {
    userId: string;
    deletedBy: string;
    sources: string[];
    type: 'hard' | 'soft' | 'anonymization';
    recordsAffected: number;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.DATA_DELETION,
      userId: data.userId,
      performedBy: data.deletedBy,
      metadata: {
        sources: data.sources,
        deletionType: data.type,
        recordsAffected: data.recordsAffected,
        timestamp: new Date(),
      },
      severity: AuditSeverity.CRITICAL,
    });
  }

  /**
   * Log data anonymization
   */
  async logDataAnonymization(data: {
    userId: string;
    performedBy: string;
    fieldsAnonymized: string[];
    method: string;
    jobId?: string;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.DATA_ANONYMIZATION,
      userId: data.userId,
      performedBy: data.performedBy,
      metadata: {
        fieldsAnonymized: data.fieldsAnonymized,
        method: data.method,
        jobId: data.jobId,
        timestamp: new Date(),
      },
      severity: AuditSeverity.HIGH,
    });
  }

  /**
   * Log data modification
   */
  async logDataModification(data: {
    userId: string;
    modifiedBy: string;
    changes: Record<string, any>;
    entityType?: string;
    entityId?: string;
    timestamp: Date;
  }): Promise<void> {
    await this.createAuditLog({
      action: AuditAction.DATA_MODIFICATION,
      userId: data.userId,
      performedBy: data.modifiedBy,
      entityType: data.entityType,
      entityId: data.entityId,
      changes: data.changes,
      severity: AuditSeverity.MEDIUM,
    });
  }

  /**
   * Log consent action
   */
  async logConsentAction(data: {
    userId: string;
    action: 'granted' | 'withdrawn' | 'updated';
    consentType: string;
    performedBy: string;
    metadata?: any;
  }): Promise<void> {
    const actionMap = {
      granted: AuditAction.CONSENT_GRANTED,
      withdrawn: AuditAction.CONSENT_WITHDRAWN,
      updated: AuditAction.CONSENT_UPDATED,
    };

    await this.createAuditLog({
      action: actionMap[data.action],
      userId: data.userId,
      performedBy: data.performedBy,
      metadata: {
        consentType: data.consentType,
        ...data.metadata,
      },
      severity: AuditSeverity.MEDIUM,
    });
  }

  /**
   * Log security event
   */
  async logSecurityEvent(data: {
    action: AuditAction;
    userId?: string;
    performedBy?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
    severity?: AuditSeverity;
  }): Promise<void> {
    await this.createAuditLog({
      action: data.action,
      userId: data.userId,
      performedBy: data.performedBy || 'system',
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: data.metadata,
      severity: data.severity || AuditSeverity.HIGH,
    });
  }

  /**
   * Search audit logs
   */
  async searchAuditLogs(
    criteria: AuditSearchCriteria,
    limit: number = 100,
    offset: number = 0,
  ): Promise<{ logs: AuditLog[]; total: number }> {
    const query = this.auditLogRepository.createQueryBuilder('audit');

    if (criteria.userId) {
      query.andWhere('audit.userId = :userId', { userId: criteria.userId });
    }

    if (criteria.performedBy) {
      query.andWhere('audit.performedBy = :performedBy', { performedBy: criteria.performedBy });
    }

    if (criteria.action) {
      query.andWhere('audit.action = :action', { action: criteria.action });
    }

    if (criteria.entityType) {
      query.andWhere('audit.entityType = :entityType', { entityType: criteria.entityType });
    }

    if (criteria.entityId) {
      query.andWhere('audit.entityId = :entityId', { entityId: criteria.entityId });
    }

    if (criteria.startDate) {
      query.andWhere('audit.createdAt >= :startDate', { startDate: criteria.startDate });
    }

    if (criteria.endDate) {
      query.andWhere('audit.createdAt <= :endDate', { endDate: criteria.endDate });
    }

    if (criteria.severity) {
      query.andWhere('audit.severity = :severity', { severity: criteria.severity });
    }

    const [logs, total] = await query
      .orderBy('audit.createdAt', 'DESC')
      .limit(limit)
      .offset(offset)
      .getManyAndCount();

    // Decrypt sensitive data
    const decryptedLogs = logs.map(log => this.decryptAuditLog(log));

    return { logs: decryptedLogs, total };
  }

  /**
   * Get audit trail for entity
   */
  async getEntityAuditTrail(
    entityType: string,
    entityId: string,
  ): Promise<AuditLog[]> {
    const logs = await this.auditLogRepository.find({
      where: {
        entityType,
        entityId,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    return logs.map(log => this.decryptAuditLog(log));
  }

  /**
   * Get user activity audit trail
   */
  async getUserActivityAuditTrail(
    userId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<AuditLog[]> {
    const query = this.auditLogRepository.createQueryBuilder('audit')
      .where('audit.userId = :userId OR audit.performedBy = :userId', { userId });

    if (startDate) {
      query.andWhere('audit.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('audit.createdAt <= :endDate', { endDate });
    }

    const logs = await query
      .orderBy('audit.createdAt', 'DESC')
      .getMany();

    return logs.map(log => this.decryptAuditLog(log));
  }

  /**
   * Generate audit report
   */
  async generateAuditReport(
    startDate: Date,
    endDate: Date,
  ): Promise<{
    summary: any;
    topActions: any[];
    topUsers: any[];
    securityEvents: any[];
  }> {
    // Summary statistics
    const summary = await this.auditLogRepository
      .createQueryBuilder('audit')
      .select('COUNT(*)', 'total')
      .addSelect('COUNT(DISTINCT audit.userId)', 'uniqueUsers')
      .addSelect('COUNT(DISTINCT audit.performedBy)', 'uniquePerformers')
      .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawOne();

    // Top actions
    const topActions = await this.auditLogRepository
      .createQueryBuilder('audit')
      .select('audit.action', 'action')
      .addSelect('COUNT(*)', 'count')
      .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('audit.action')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();

    // Top users by activity
    const topUsers = await this.auditLogRepository
      .createQueryBuilder('audit')
      .select('audit.performedBy', 'user')
      .addSelect('COUNT(*)', 'actionCount')
      .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('audit.performedBy')
      .orderBy('actionCount', 'DESC')
      .limit(10)
      .getRawMany();

    // Security events
    const securityEvents = await this.auditLogRepository
      .createQueryBuilder('audit')
      .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('audit.severity IN (:...severities)', {
        severities: [AuditSeverity.HIGH, AuditSeverity.CRITICAL],
      })
      .orderBy('audit.createdAt', 'DESC')
      .limit(50)
      .getMany();

    return {
      summary,
      topActions,
      topUsers,
      securityEvents: securityEvents.map(log => this.decryptAuditLog(log)),
    };
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldAuditLogs(): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

    const result = await this.auditLogRepository
      .createQueryBuilder()
      .delete()
      .where('createdAt < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`Cleaned up ${result.affected} old audit logs`);

    return result.affected || 0;
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(
    criteria: AuditSearchCriteria,
    format: 'json' | 'csv' = 'json',
  ): Promise<{ data: string; filename: string }> {
    const { logs } = await this.searchAuditLogs(criteria, 10000, 0);

    let data: string;
    let filename: string;

    if (format === 'json') {
      data = JSON.stringify(logs, null, 2);
      filename = `audit_logs_${Date.now()}.json`;
    } else {
      // Convert to CSV format
      const headers = [
        'Timestamp',
        'Action',
        'User ID',
        'Performed By',
        'Entity Type',
        'Entity ID',
        'Severity',
        'IP Address',
      ];

      const rows = logs.map(log => [
        log.createdAt.toISOString(),
        log.action,
        log.userId || '',
        log.performedBy,
        log.entityType || '',
        log.entityId || '',
        log.severity,
        log.ipAddress || '',
      ]);

      data = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      filename = `audit_logs_${Date.now()}.csv`;
    }

    return { data, filename };
  }

  // Private helper methods

  private async createAuditLog(entry: AuditLogEntry): Promise<void> {
    try {
      // Encrypt sensitive data
      const encryptedData = this.encryptSensitiveData(entry);

      const auditLog = this.auditLogRepository.create({
        action: entry.action,
        userId: entry.userId,
        performedBy: entry.performedBy,
        entityType: entry.entityType,
        entityId: entry.entityId,
        changes: encryptedData.changes,
        metadata: encryptedData.metadata,
        severity: entry.severity || this.determineSeverityForAction(entry.action),
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        checksum: this.calculateChecksum(entry),
      });

      await this.auditLogRepository.save(auditLog);

      // Emit audit event
      this.eventEmitter.emit('audit.log.created', {
        action: entry.action,
        severity: auditLog.severity,
        timestamp: new Date(),
      });

      // Check for suspicious patterns
      await this.checkSuspiciousActivity(entry);
    } catch (error) {
      this.logger.error('Failed to create audit log', error);
      // Audit logging should not fail operations
    }
  }

  private encryptSensitiveData(entry: AuditLogEntry): {
    changes?: string;
    metadata?: string;
  } {
    const encrypted: any = {};

    if (entry.changes) {
      encrypted.changes = this.encrypt(JSON.stringify(entry.changes));
    }

    if (entry.metadata) {
      encrypted.metadata = this.encrypt(JSON.stringify(entry.metadata));
    }

    return encrypted;
  }

  private decryptAuditLog(log: AuditLog): AuditLog {
    const decrypted = { ...log };

    if (log.changes) {
      try {
        decrypted.changes = JSON.parse(this.decrypt(log.changes));
      } catch (error) {
        this.logger.error('Failed to decrypt changes', error);
      }
    }

    if (log.metadata) {
      try {
        decrypted.metadata = JSON.parse(this.decrypt(log.metadata));
      } catch (error) {
        this.logger.error('Failed to decrypt metadata', error);
      }
    }

    return decrypted;
  }

  private encrypt(data: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  private decrypt(encryptedData: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  private calculateChecksum(entry: AuditLogEntry): string {
    const data = JSON.stringify({
      action: entry.action,
      userId: entry.userId,
      performedBy: entry.performedBy,
      timestamp: new Date().toISOString(),
    });

    return crypto
      .createHash('sha256')
      .update(data)
      .digest('hex');
  }

  private determineSeverity(dataCategories: string[]): AuditSeverity {
    const sensitiveCategoris = ['financial', 'health', 'biometric', 'legal'];
    
    if (dataCategories.some(cat => sensitiveCategoris.includes(cat))) {
      return AuditSeverity.HIGH;
    }
    
    return AuditSeverity.MEDIUM;
  }

  private determineSeverityForAction(action: AuditAction): AuditSeverity {
    const severityMap: Record<AuditAction, AuditSeverity> = {
      [AuditAction.DATA_DELETION]: AuditSeverity.CRITICAL,
      [AuditAction.DATA_EXPORT]: AuditSeverity.HIGH,
      [AuditAction.DATA_ANONYMIZATION]: AuditSeverity.HIGH,
      [AuditAction.PRIVACY_REQUEST_CREATED]: AuditSeverity.HIGH,
      [AuditAction.AUTHENTICATION_FAILURE]: AuditSeverity.HIGH,
      [AuditAction.SUSPICIOUS_ACTIVITY]: AuditSeverity.CRITICAL,
      [AuditAction.PERMISSION_CHANGED]: AuditSeverity.HIGH,
      [AuditAction.CONFIGURATION_CHANGED]: AuditSeverity.HIGH,
      [AuditAction.DATA_ACCESS]: AuditSeverity.MEDIUM,
      [AuditAction.DATA_MODIFICATION]: AuditSeverity.MEDIUM,
      [AuditAction.CONSENT_GRANTED]: AuditSeverity.LOW,
      [AuditAction.CONSENT_WITHDRAWN]: AuditSeverity.MEDIUM,
      [AuditAction.AUTHENTICATION_SUCCESS]: AuditSeverity.LOW,
      [AuditAction.USER_CREATED]: AuditSeverity.MEDIUM,
      [AuditAction.USER_UPDATED]: AuditSeverity.LOW,
      [AuditAction.USER_DELETED]: AuditSeverity.HIGH,
      [AuditAction.CONSENT_UPDATED]: AuditSeverity.LOW,
      [AuditAction.AUTHORIZATION_CHECK]: AuditSeverity.LOW,
      [AuditAction.PRIVACY_REQUEST_COMPLETED]: AuditSeverity.MEDIUM,
      [AuditAction.PRIVACY_REQUEST_FAILED]: AuditSeverity.HIGH,
    };

    return severityMap[action] || AuditSeverity.MEDIUM;
  }

  private async checkSuspiciousActivity(entry: AuditLogEntry): Promise<void> {
    // Check for suspicious patterns
    if (entry.action === AuditAction.AUTHENTICATION_FAILURE) {
      // Check for multiple failed attempts
      const recentFailures = await this.auditLogRepository.count({
        where: {
          action: AuditAction.AUTHENTICATION_FAILURE,
          performedBy: entry.performedBy,
          createdAt: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      });

      if (recentFailures >= 5) {
        await this.createAuditLog({
          action: AuditAction.SUSPICIOUS_ACTIVITY,
          performedBy: entry.performedBy,
          metadata: {
            reason: 'Multiple authentication failures',
            failureCount: recentFailures,
          },
          severity: AuditSeverity.CRITICAL,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
        });
      }
    }

    // Check for bulk data access
    if (entry.action === AuditAction.DATA_ACCESS || entry.action === AuditAction.DATA_EXPORT) {
      const recentAccess = await this.auditLogRepository.count({
        where: {
          action: entry.action,
          performedBy: entry.performedBy,
          createdAt: new Date(Date.now() - 10 * 60 * 1000), // Last 10 minutes
        },
      });

      if (recentAccess >= 10) {
        await this.createAuditLog({
          action: AuditAction.SUSPICIOUS_ACTIVITY,
          performedBy: entry.performedBy,
          metadata: {
            reason: 'Bulk data access detected',
            accessCount: recentAccess,
          },
          severity: AuditSeverity.HIGH,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
        });
      }
    }
  }
}