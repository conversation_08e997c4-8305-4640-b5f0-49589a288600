import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';

interface AnonymizationRule {
  field: string;
  method: AnonymizationMethod;
  options?: any;
}

enum AnonymizationMethod {
  HASH = 'hash',
  RANDOM = 'random',
  MASK = 'mask',
  GENERALIZE = 'generalize',
  SUPPRESS = 'suppress',
  ENCRYPT = 'encrypt',
  TOKENIZE = 'tokenize',
}

@Injectable()
export class DataAnonymizationService {
  private readonly logger = new Logger(DataAnonymizationService.name);
  private readonly salt: string;
  private readonly defaultRules: AnonymizationRule[];

  constructor(private readonly configService: ConfigService) {
    this.salt = this.configService.get('ANONYMIZATION_SALT', 'default-salt');
    this.defaultRules = this.getDefaultAnonymizationRules();
  }

  /**
   * Anonymize user data according to GDPR requirements
   */
  async anonymizeUserData(userId: string, data: any): Promise<any> {
    this.logger.log(`Anonymizing data for user ${userId}`);

    const anonymized = await this.applyAnonymizationRules(data, this.defaultRules);
    
    // Generate anonymous ID
    anonymized.id = this.generateAnonymousId(userId);
    anonymized.anonymizedAt = new Date();
    anonymized.isAnonymized = true;

    return anonymized;
  }

  /**
   * Anonymize specific fields
   */
  async anonymizeSensitiveFields(
    data: any,
    fieldsToExclude: string[] = [],
  ): Promise<any> {
    const rules = this.defaultRules.filter(
      rule => !fieldsToExclude.includes(rule.field)
    );

    return this.applyAnonymizationRules(data, rules);
  }

  /**
   * Apply k-anonymity to dataset
   */
  async applyKAnonymity(
    dataset: any[],
    k: number,
    quasiIdentifiers: string[],
  ): Promise<any[]> {
    // Group records by quasi-identifiers
    const groups = this.groupByQuasiIdentifiers(dataset, quasiIdentifiers);

    // Suppress groups with less than k records
    const kAnonymousGroups = Array.from(groups.entries()).filter(
      ([_, records]) => records.length >= k
    );

    // Generalize quasi-identifiers for remaining groups
    const anonymized = [];
    for (const [_, records] of kAnonymousGroups) {
      const generalizedRecords = records.map(record => 
        this.generalizeQuasiIdentifiers(record, quasiIdentifiers)
      );
      anonymized.push(...generalizedRecords);
    }

    return anonymized;
  }

  /**
   * Apply differential privacy
   */
  async applyDifferentialPrivacy(
    data: number,
    epsilon: number = 1.0,
  ): Promise<number> {
    // Add Laplace noise for differential privacy
    const sensitivity = 1; // Assuming normalized data
    const scale = sensitivity / epsilon;
    const noise = this.laplacianNoise(scale);
    
    return data + noise;
  }

  /**
   * Generate anonymous ID
   */
  generateAnonymousId(originalId: string): string {
    return crypto
      .createHash('sha256')
      .update(`${originalId}${this.salt}`)
      .digest('hex')
      .substring(0, 16);
  }

  /**
   * Mask sensitive data
   */
  maskData(value: string, maskPattern: string = '*'): string {
    if (!value) return value;

    const length = value.length;
    if (length <= 4) {
      return maskPattern.repeat(length);
    }

    // Show first and last 2 characters
    const start = value.substring(0, 2);
    const end = value.substring(length - 2);
    const middle = maskPattern.repeat(length - 4);

    return `${start}${middle}${end}`;
  }

  /**
   * Tokenize sensitive data
   */
  async tokenizeData(value: string): Promise<string> {
    const token = crypto.randomBytes(16).toString('hex');
    
    // In production, store mapping in secure token vault
    // For now, return the token
    return `token_${token}`;
  }

  /**
   * Apply pseudonymization
   */
  pseudonymize(value: string, context: string = ''): string {
    return crypto
      .createHash('sha256')
      .update(`${value}${context}${this.salt}`)
      .digest('hex');
  }

  // Private helper methods

  private getDefaultAnonymizationRules(): AnonymizationRule[] {
    return [
      // Personal identifiers
      { field: 'email', method: AnonymizationMethod.HASH },
      { field: 'name', method: AnonymizationMethod.RANDOM },
      { field: 'firstName', method: AnonymizationMethod.RANDOM },
      { field: 'lastName', method: AnonymizationMethod.RANDOM },
      { field: 'phone', method: AnonymizationMethod.MASK },
      { field: 'ssn', method: AnonymizationMethod.SUPPRESS },
      { field: 'dob', method: AnonymizationMethod.GENERALIZE, options: { level: 'year' } },
      
      // Location data
      { field: 'address', method: AnonymizationMethod.GENERALIZE, options: { level: 'city' } },
      { field: 'zipCode', method: AnonymizationMethod.GENERALIZE, options: { level: 'region' } },
      { field: 'geoLocation', method: AnonymizationMethod.GENERALIZE, options: { precision: 1 } },
      
      // Financial data
      { field: 'creditCard', method: AnonymizationMethod.TOKENIZE },
      { field: 'bankAccount', method: AnonymizationMethod.TOKENIZE },
      { field: 'salary', method: AnonymizationMethod.GENERALIZE, options: { range: 10000 } },
      
      // Health data
      { field: 'healthRecords', method: AnonymizationMethod.ENCRYPT },
      { field: 'medicalId', method: AnonymizationMethod.HASH },
      
      // Online identifiers
      { field: 'ipAddress', method: AnonymizationMethod.MASK },
      { field: 'deviceId', method: AnonymizationMethod.HASH },
      { field: 'cookieId', method: AnonymizationMethod.SUPPRESS },
    ];
  }

  private async applyAnonymizationRules(
    data: any,
    rules: AnonymizationRule[],
  ): Promise<any> {
    const anonymized = { ...data };

    for (const rule of rules) {
      const value = this.getNestedValue(anonymized, rule.field);
      if (value !== undefined) {
        const anonymizedValue = await this.applyAnonymizationMethod(
          value,
          rule.method,
          rule.options,
        );
        this.setNestedValue(anonymized, rule.field, anonymizedValue);
      }
    }

    return anonymized;
  }

  private async applyAnonymizationMethod(
    value: any,
    method: AnonymizationMethod,
    options?: any,
  ): Promise<any> {
    switch (method) {
      case AnonymizationMethod.HASH:
        return this.pseudonymize(String(value));

      case AnonymizationMethod.RANDOM:
        return this.generateRandomReplacement(value);

      case AnonymizationMethod.MASK:
        return this.maskData(String(value));

      case AnonymizationMethod.GENERALIZE:
        return this.generalizeValue(value, options);

      case AnonymizationMethod.SUPPRESS:
        return null;

      case AnonymizationMethod.ENCRYPT:
        return this.encryptValue(value);

      case AnonymizationMethod.TOKENIZE:
        return this.tokenizeData(String(value));

      default:
        return value;
    }
  }

  private generateRandomReplacement(value: any): any {
    if (typeof value === 'string') {
      // Generate random name-like string
      const names = ['User', 'Person', 'Individual', 'Member', 'Participant'];
      const randomIndex = Math.floor(Math.random() * names.length);
      const randomId = crypto.randomBytes(4).toString('hex');
      return `${names[randomIndex]}_${randomId}`;
    }
    return 'ANONYMIZED';
  }

  private generalizeValue(value: any, options: any): any {
    if (value instanceof Date || !isNaN(Date.parse(value))) {
      // Generalize date
      const date = new Date(value);
      if (options?.level === 'year') {
        return date.getFullYear();
      } else if (options?.level === 'month') {
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      }
      return date.toISOString().split('T')[0];
    }

    if (typeof value === 'number') {
      // Generalize number to range
      const range = options?.range || 10;
      return Math.floor(value / range) * range;
    }

    if (typeof value === 'string' && options?.level) {
      // Generalize location
      if (options.level === 'city') {
        return 'City';
      } else if (options.level === 'region') {
        return 'Region';
      }
    }

    return 'GENERALIZED';
  }

  private encryptValue(value: any): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.salt, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    
    let encrypted = cipher.update(JSON.stringify(value), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  private groupByQuasiIdentifiers(
    dataset: any[],
    quasiIdentifiers: string[],
  ): Map<string, any[]> {
    const groups = new Map<string, any[]>();

    for (const record of dataset) {
      const key = quasiIdentifiers
        .map(qi => this.getNestedValue(record, qi))
        .join('|');
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(record);
    }

    return groups;
  }

  private generalizeQuasiIdentifiers(
    record: any,
    quasiIdentifiers: string[],
  ): any {
    const generalized = { ...record };

    for (const qi of quasiIdentifiers) {
      const value = this.getNestedValue(generalized, qi);
      if (value !== undefined) {
        const generalizedValue = this.generalizeValue(value, { level: 'high' });
        this.setNestedValue(generalized, qi, generalizedValue);
      }
    }

    return generalized;
  }

  private laplacianNoise(scale: number): number {
    // Generate Laplacian noise for differential privacy
    const u = Math.random() - 0.5;
    return -scale * Math.sign(u) * Math.log(1 - 2 * Math.abs(u));
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key]) {
        current[key] = {};
      }
      return current[key];
    }, obj);

    target[lastKey] = value;
  }
}