import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { firstValueFrom } from 'rxjs';
import { DataAnonymizationService } from './data-anonymization.service';

interface DeletionResult {
  source: string;
  status: 'deleted' | 'anonymized' | 'failed' | 'skipped';
  recordsAffected?: number;
  error?: string;
  retainedData?: string[];
}

interface DeletionOptions {
  hardDelete: boolean;
  anonymizeInstead?: boolean;
  retainForLegal?: boolean;
  retentionPeriod?: number;
  excludeSources?: string[];
}

interface DataRetentionRule {
  category: string;
  minimumRetentionDays: number;
  reason: string;
  legalBasis?: string;
}

@Injectable()
export class DataDeletionService {
  private readonly logger = new Logger(DataDeletionService.name);
  private readonly retentionRules: DataRetentionRule[];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly dataAnonymizationService: DataAnonymizationService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.retentionRules = this.getRetentionRules();
  }

  /**
   * Delete or anonymize user data across all systems
   */
  async deleteUserData(
    userId: string,
    options: DeletionOptions = { hardDelete: false },
  ): Promise<DeletionResult[]> {
    this.logger.log(`Processing data deletion for user ${userId}`, options);

    // Check retention requirements
    const retentionCheck = await this.checkRetentionRequirements(userId);
    if (!retentionCheck.canDelete) {
      throw new Error(`Cannot delete data: ${retentionCheck.reason}`);
    }

    const results: DeletionResult[] = [];
    const dataSources = this.getDataSources();

    // Process each data source
    for (const source of dataSources) {
      if (options.excludeSources?.includes(source.name)) {
        results.push({
          source: source.name,
          status: 'skipped',
        });
        continue;
      }

      try {
        const result = await this.deleteFromSource(source, userId, options);
        results.push(result);

        // Emit deletion event
        this.eventEmitter.emit('privacy.data.deleted', {
          userId,
          source: source.name,
          status: result.status,
          timestamp: new Date(),
        });
      } catch (error) {
        this.logger.error(`Failed to delete from ${source.name}`, error);
        results.push({
          source: source.name,
          status: 'failed',
          error: error.message,
        });
      }
    }

    // Log deletion summary
    await this.logDeletionSummary(userId, results, options);

    return results;
  }

  /**
   * Soft delete user data (mark as deleted without removing)
   */
  async softDeleteUserData(userId: string): Promise<DeletionResult[]> {
    this.logger.log(`Soft deleting data for user ${userId}`);

    const results: DeletionResult[] = [];
    const dataSources = this.getDataSources();

    for (const source of dataSources) {
      try {
        const result = await this.softDeleteFromSource(source, userId);
        results.push(result);
      } catch (error) {
        this.logger.error(`Failed to soft delete from ${source.name}`, error);
        results.push({
          source: source.name,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return results;
  }

  /**
   * Schedule data deletion after retention period
   */
  async scheduleDataDeletion(
    userId: string,
    deletionDate: Date,
    options: DeletionOptions,
  ): Promise<{ scheduledId: string; deletionDate: Date }> {
    const scheduledId = this.generateScheduledDeletionId(userId);

    // Store scheduled deletion
    await this.storeScheduledDeletion({
      scheduledId,
      userId,
      deletionDate,
      options,
      createdAt: new Date(),
    });

    // Emit event for scheduled deletion
    this.eventEmitter.emit('privacy.deletion.scheduled', {
      scheduledId,
      userId,
      deletionDate,
      options,
    });

    return { scheduledId, deletionDate };
  }

  /**
   * Cancel scheduled deletion
   */
  async cancelScheduledDeletion(scheduledId: string): Promise<void> {
    await this.removeScheduledDeletion(scheduledId);

    this.eventEmitter.emit('privacy.deletion.cancelled', {
      scheduledId,
      cancelledAt: new Date(),
    });
  }

  /**
   * Process scheduled deletions
   */
  async processScheduledDeletions(): Promise<void> {
    const dueDeletions = await this.getDueDeletions();

    for (const deletion of dueDeletions) {
      try {
        await this.deleteUserData(deletion.userId, deletion.options);
        await this.removeScheduledDeletion(deletion.scheduledId);
      } catch (error) {
        this.logger.error(
          `Failed to process scheduled deletion ${deletion.scheduledId}`,
          error,
        );
      }
    }
  }

  /**
   * Get deletion status
   */
  async getDeletionStatus(userId: string): Promise<{
    isDeleted: boolean;
    deletionDate?: Date;
    retainedData?: string[];
    anonymizedData?: string[];
  }> {
    const deletionRecord = await this.getDeletionRecord(userId);

    if (!deletionRecord) {
      return { isDeleted: false };
    }

    return {
      isDeleted: true,
      deletionDate: deletionRecord.deletionDate,
      retainedData: deletionRecord.retainedData,
      anonymizedData: deletionRecord.anonymizedData,
    };
  }

  /**
   * Verify complete data deletion
   */
  async verifyDataDeletion(userId: string): Promise<{
    verified: boolean;
    remainingData?: string[];
  }> {
    const dataSources = this.getDataSources();
    const remainingData: string[] = [];

    for (const source of dataSources) {
      const hasData = await this.checkDataExists(source, userId);
      if (hasData) {
        remainingData.push(source.name);
      }
    }

    return {
      verified: remainingData.length === 0,
      remainingData: remainingData.length > 0 ? remainingData : undefined,
    };
  }

  // Private helper methods

  private async deleteFromSource(
    source: any,
    userId: string,
    options: DeletionOptions,
  ): Promise<DeletionResult> {
    const endpoint = `${source.endpoint}/${userId}`;

    if (options.anonymizeInstead) {
      // Anonymize data instead of deleting
      const anonymizedData = await this.dataAnonymizationService.anonymizeUserData(
        userId,
        {},
      );

      const response = await firstValueFrom(
        this.httpService.put(endpoint, anonymizedData, {
          headers: {
            'X-Operation': 'anonymize',
            'X-User-Id': userId,
          },
        }),
      );

      return {
        source: source.name,
        status: 'anonymized',
        recordsAffected: response.data.recordsAffected,
      };
    }

    // Hard delete
    const response = await firstValueFrom(
      this.httpService.delete(endpoint, {
        headers: {
          'X-Hard-Delete': options.hardDelete.toString(),
          'X-User-Id': userId,
        },
        params: {
          retainForLegal: options.retainForLegal,
        },
      }),
    );

    return {
      source: source.name,
      status: 'deleted',
      recordsAffected: response.data.recordsAffected,
      retainedData: response.data.retainedData,
    };
  }

  private async softDeleteFromSource(
    source: any,
    userId: string,
  ): Promise<DeletionResult> {
    const endpoint = `${source.endpoint}/${userId}/soft-delete`;

    const response = await firstValueFrom(
      this.httpService.post(endpoint, {}, {
        headers: {
          'X-User-Id': userId,
        },
      }),
    );

    return {
      source: source.name,
      status: 'deleted',
      recordsAffected: response.data.recordsAffected,
    };
  }

  private async checkDataExists(source: any, userId: string): Promise<boolean> {
    try {
      const response = await firstValueFrom(
        this.httpService.head(`${source.endpoint}/${userId}`, {
          headers: {
            'X-User-Id': userId,
          },
        }),
      );
      return response.status === 200;
    } catch (error) {
      if (error.response?.status === 404) {
        return false;
      }
      throw error;
    }
  }

  private async checkRetentionRequirements(userId: string): Promise<{
    canDelete: boolean;
    reason?: string;
    retentionRequired?: string[];
  }> {
    const retentionRequired: string[] = [];

    // Check each retention rule
    for (const rule of this.retentionRules) {
      const hasRecentData = await this.hasRecentData(userId, rule);
      if (hasRecentData) {
        retentionRequired.push(rule.category);
      }
    }

    // Check legal holds
    const legalHold = await this.checkLegalHold(userId);
    if (legalHold) {
      return {
        canDelete: false,
        reason: 'Legal hold in place',
      };
    }

    // Check active services
    const activeServices = await this.checkActiveServices(userId);
    if (activeServices.length > 0) {
      return {
        canDelete: false,
        reason: `Active services: ${activeServices.join(', ')}`,
      };
    }

    if (retentionRequired.length > 0) {
      return {
        canDelete: false,
        reason: 'Retention period not met',
        retentionRequired,
      };
    }

    return { canDelete: true };
  }

  private async hasRecentData(
    userId: string,
    rule: DataRetentionRule,
  ): Promise<boolean> {
    // Check if user has data within retention period
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - rule.minimumRetentionDays);

    // This would check against actual data sources
    return false; // Placeholder
  }

  private async checkLegalHold(userId: string): Promise<boolean> {
    // Check if user data is under legal hold
    // This would integrate with legal/compliance systems
    return false; // Placeholder
  }

  private async checkActiveServices(userId: string): Promise<string[]> {
    // Check for active subscriptions, pending transactions, etc.
    return []; // Placeholder
  }

  private getDataSources(): any[] {
    return [
      {
        name: 'E-Connect',
        endpoint: 'http://localhost:3001/api/user-data',
        category: 'emails',
      },
      {
        name: 'Lighthouse',
        endpoint: 'http://localhost:3002/api/user-data',
        category: 'research',
      },
      {
        name: 'Training',
        endpoint: 'http://localhost:3003/api/user-data',
        category: 'training',
      },
      {
        name: 'Vendors',
        endpoint: 'http://localhost:3004/api/user-data',
        category: 'vendors',
      },
      {
        name: 'Wins',
        endpoint: 'http://localhost:3005/api/user-data',
        category: 'wins',
      },
      {
        name: 'AMNA',
        endpoint: 'http://localhost:3000/api/user-data',
        category: 'chat',
      },
    ];
  }

  private getRetentionRules(): DataRetentionRule[] {
    return [
      {
        category: 'financial',
        minimumRetentionDays: 2555, // 7 years
        reason: 'Tax and accounting requirements',
        legalBasis: 'Legal obligation',
      },
      {
        category: 'contracts',
        minimumRetentionDays: 2190, // 6 years
        reason: 'Contract statute of limitations',
        legalBasis: 'Legal obligation',
      },
      {
        category: 'employment',
        minimumRetentionDays: 1095, // 3 years
        reason: 'Employment law requirements',
        legalBasis: 'Legal obligation',
      },
      {
        category: 'audit_logs',
        minimumRetentionDays: 365, // 1 year
        reason: 'Security and compliance',
        legalBasis: 'Legitimate interest',
      },
      {
        category: 'support_tickets',
        minimumRetentionDays: 180, // 6 months
        reason: 'Customer service quality',
        legalBasis: 'Legitimate interest',
      },
    ];
  }

  private generateScheduledDeletionId(userId: string): string {
    return `del_${userId}_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  }

  private async storeScheduledDeletion(deletion: any): Promise<void> {
    // Store in database or persistent storage
    // This is a placeholder - implement actual storage
  }

  private async removeScheduledDeletion(scheduledId: string): Promise<void> {
    // Remove from database or persistent storage
    // This is a placeholder - implement actual removal
  }

  private async getDueDeletions(): Promise<any[]> {
    // Get deletions due for processing
    // This is a placeholder - implement actual query
    return [];
  }

  private async getDeletionRecord(userId: string): Promise<any> {
    // Get deletion record for user
    // This is a placeholder - implement actual query
    return null;
  }

  private async logDeletionSummary(
    userId: string,
    results: DeletionResult[],
    options: DeletionOptions,
  ): Promise<void> {
    const summary = {
      userId,
      timestamp: new Date(),
      options,
      results: results.map(r => ({
        source: r.source,
        status: r.status,
        recordsAffected: r.recordsAffected,
      })),
      totalDeleted: results.filter(r => r.status === 'deleted').length,
      totalAnonymized: results.filter(r => r.status === 'anonymized').length,
      totalFailed: results.filter(r => r.status === 'failed').length,
    };

    this.logger.log('Data deletion summary', summary);

    // Store deletion log for compliance
    this.eventEmitter.emit('privacy.deletion.completed', summary);
  }
}