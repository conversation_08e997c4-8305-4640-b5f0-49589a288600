import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConsentManagementService, ConsentType } from '../services/consent-management.service';

export const REQUIRED_CONSENTS_KEY = 'requiredConsents';

export const RequireConsents = (...consents: ConsentType[]) =>
  Reflect.metadata(REQUIRED_CONSENTS_KEY, consents);

@Injectable()
export class ConsentGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly consentService: ConsentManagementService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredConsents = this.reflector.getAllAndOverride<ConsentType[]>(
      REQUIRED_CONSENTS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredConsents || requiredConsents.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    const consentCheck = await this.consentService.checkConsentRequirements(
      user.id,
      requiredConsents,
    );

    if (!consentCheck.allowed) {
      throw new ForbiddenException({
        message: 'Required consents not granted',
        missingConsents: consentCheck.missingConsents,
        consentUrl: '/consent/preferences',
      });
    }

    return true;
  }
}