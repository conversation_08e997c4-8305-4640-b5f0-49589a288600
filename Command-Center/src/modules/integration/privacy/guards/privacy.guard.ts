import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuditService, AuditAction } from '../services/audit.service';

@Injectable()
export class PrivacyGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly auditService: AuditService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Log privacy-related access
    await this.auditService.logSecurityEvent({
      action: AuditAction.AUTHORIZATION_CHECK,
      userId: user.id,
      performedBy: user.id,
      ipAddress: request.ip,
      userAgent: request.headers['user-agent'],
      metadata: {
        endpoint: request.url,
        method: request.method,
        controller: context.getClass().name,
        handler: context.getHandler().name,
      },
    });

    // Check if user is accessing their own data
    const params = request.params;
    const userId = params.userId || params.id;

    if (userId && userId !== user.id) {
      // User is trying to access another user's data
      // Check if they have appropriate permissions
      const hasAdminRole = user.roles?.includes('admin') || user.roles?.includes('dpo');
      
      if (!hasAdminRole) {
        await this.auditService.logSecurityEvent({
          action: AuditAction.SUSPICIOUS_ACTIVITY,
          userId: user.id,
          performedBy: user.id,
          ipAddress: request.ip,
          userAgent: request.headers['user-agent'],
          metadata: {
            reason: 'Attempted to access another user\'s privacy data',
            targetUserId: userId,
            endpoint: request.url,
          },
          severity: 'high',
        });

        return false;
      }
    }

    return true;
  }
}